# Cost and Time Estimates

## 💰 OpenAI API Costs (o4-mini with Batch API)

### Pricing
- **Input tokens**: $0.55 per 1M tokens (50% discount)
- **Output tokens**: $2.20 per 1M tokens (50% discount)

### Actual Results
- **1000 samples requested**: 794 successful (79.4% success rate)
- **Total cost**: ~$15-50 for the entire dataset
- **Cost per sample**: ~$0.02-0.06

## ⏱️ Training Time Estimates

### Hardware Requirements
- **GPU**: NVIDIA A100-80GB (recommended)
- **Memory**: ~40-60GB VRAM during training
- **Storage**: ~20GB for model checkpoints

### Training Time
- **Per model**: 2-4 hours on A100-80GB
- **Both models**: 4-8 hours total
- **794 samples**: Sufficient for effective fine-tuning

## 📊 Dataset Statistics

### Final Synchronized Dataset
- **Original samples**: 794 mathematical problems
- **Code-switched samples**: 794 Korean-English mixed solutions
- **Success rate**: 79.4% (excellent for batch processing)
- **Quality**: High-quality code-switched mathematical reasoning

### Data Characteristics
- **Average length**: 1000-3000 tokens per sample
- **Languages**: Korean-English code-switching
- **Domain**: Mathematical problem solving
- **Format**: Conversational (user question + assistant solution)

## 🎯 Total Project Costs

| Component | Cost | Time |
|-----------|------|------|
| Data creation (OpenAI API) | $15-50 | 1-3 hours |
| Model training (2 models) | Free (local GPU) | 4-8 hours |
| **Total** | **$15-50** | **5-11 hours** |

## 💡 Cost Optimization

### What We Did Right
- ✅ Used Batch API (50% cost savings)
- ✅ Used o4-mini instead of o3-pro (97% cost savings)
- ✅ Processed only shortest samples (efficiency)
- ✅ Synchronized datasets (fair comparison)

### Recommendations
- Start with 10-100 samples for testing
- Use Batch API for all large-scale processing
- Monitor OpenAI usage dashboard
- Keep synchronized datasets for fair model comparison
