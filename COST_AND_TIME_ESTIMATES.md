# Cost and Time Estimates for o4-mini-2025-04-16 with Batch API

## ✅ Great News!

The pipeline has been updated to use **OpenAI's o4-mini-2025-04-16** with **Batch API** support. This provides excellent reasoning capabilities at a fraction of the cost of o3-pro, with much faster processing times.

## 💰 Cost Breakdown

### OpenAI o4-mini Batch API Pricing
- **Input tokens**: $0.55 per 1M tokens (50% discount from regular API)
- **Output tokens**: $2.20 per 1M tokens (50% discount from regular API)
- **Regular API**: $1.10 input + $4.40 output per 1M tokens

### Estimated Costs per Sample
Each LIMO mathematical solution typically generates:
- **Input**: ~1,000-3,000 tokens (system prompt + original solution)
- **Output**: ~2,000-8,000 tokens (code-switched solution)
- **Total**: ~3,000-11,000 tokens per sample
- **Cost per sample (Batch API)**: ~$0.015-$0.05

### Total Cost Estimates (Batch API)

| Samples | Estimated Cost | Recommended Use |
|---------|---------------|-----------------|
| 10      | $0.15-$0.50   | Initial testing |
| 50      | $0.75-$2.50   | Small validation |
| 100     | $1.50-$5.00   | Medium test |
| 500     | $7.50-$25     | Large validation |
| 1000    | $15-$50       | Full dataset |

## ⏱️ Time Estimates

### Batch Processing Time
- **Batch submission**: ~1-2 minutes
- **Batch processing**: ~5-30 seconds per sample (parallel processing)
- **Batch completion**: ~5-60 minutes depending on queue
- **Results download**: ~1-2 minutes

### Total Time Estimates (Batch API)

| Samples | Batch Processing Time | Total Time | Recommended |
|---------|----------------------|------------|-------------|
| 10      | ~5-15 minutes        | ~10-20 min | ✅ Perfect for testing |
| 50      | ~10-25 minutes       | ~15-30 min | ✅ Great for validation |
| 100     | ~15-30 minutes       | ~20-35 min | ✅ Excellent for medium test |
| 500     | ~30-90 minutes       | ~35-95 min | ✅ Good for large validation |
| 1000    | ~60-180 minutes      | ~65-185 min| ✅ Very reasonable for full dataset |

## 🎯 Recommendations

### For First-Time Users
1. **Start with 10 samples** (~$0.15-0.50, 10-20 minutes)
2. Verify the code-switching quality
3. Check your OpenAI API usage dashboard
4. Scale up to 100 samples for validation

### For Research/Production
1. **Budget reasonably**: 1000 samples = $15-50 (very affordable!)
2. **Plan for time**: Full dataset takes 1-3 hours (much faster!)
3. **Use Batch API**: Automatic 50% cost savings
4. **Monitor progress**: Batch API provides clear status updates

### Cost Optimization Tips
1. **Always use Batch API**: 50% cost savings automatically
2. **Start with smaller batches**: Test with 10-100 samples first
3. **Monitor batch status**: Check progress in OpenAI dashboard
4. **Batch efficiently**: Process larger batches for better efficiency

## 🔧 Configuration Recommendations

### Small Test (Recommended for first-time users)
```bash
python run_pipeline.py \
    --num_samples 10 \
    --use_batch_api
```

### Medium Test
```bash
python run_pipeline.py \
    --num_samples 100 \
    --use_batch_api \
    --batch_size 100
```

### Production Settings (Full dataset)
```bash
python run_pipeline.py \
    --num_samples 1000 \
    --use_batch_api \
    --batch_size 100
```

### Regular API (if needed)
```bash
python run_pipeline.py \
    --num_samples 50 \
    --max_concurrent 10 \
    --requests_per_minute 1000
```

## 📊 Comparison with Previous Models

| Aspect | o1-mini | o3-pro | o4-mini (Batch) | Best Choice |
|--------|---------|--------|-----------------|-------------|
| Cost per 1M tokens | ~$7.50 avg | ~$50 avg | ~$1.38 avg | **o4-mini** |
| Processing time | 10-30 seconds | 2-5 minutes | 5-30 seconds | **o4-mini** |
| Reasoning quality | Good | Excellent | Excellent | o3-pro/o4-mini |
| 1000 samples cost | $20-50 | $1,500-5,000 | $15-50 | **o4-mini** |
| 1000 samples time | 3-8 hours | 2-3 days | 1-3 hours | **o4-mini** |
| Batch API support | No | No | Yes | **o4-mini** |

## 🛡️ Safety Features

The pipeline includes several safety features to prevent runaway costs:

1. **Rate limiting**: Configurable requests per minute
2. **Progress saving**: Saves progress every 50 samples
3. **Cost estimation**: Real-time cost tracking
4. **Error handling**: Robust retry logic with backoff
5. **Confirmation prompts**: Multiple warnings for large batches

## 🚨 Important Warnings

1. **Budget carefully**: o3-pro can be very expensive
2. **Start small**: Always test with 5-10 samples first
3. **Monitor costs**: Check OpenAI dashboard frequently
4. **Set limits**: Configure API spending limits
5. **Plan time**: Large batches take days to complete
6. **Check quality**: Verify output quality before scaling up

## 📞 Support

If you encounter issues or have questions about costs:
1. Check the OpenAI API dashboard for real-time usage
2. Review the processing logs for detailed cost breakdowns
3. Start with smaller batches to validate the pipeline
4. Monitor the `processing_stats.json` file for detailed metrics
