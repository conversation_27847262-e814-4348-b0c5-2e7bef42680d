# KULLM-Pro: Fine-tuning Qwen2.5-7B-Instruct on GAIR/LIMO Dataset

This project provides a complete pipeline for fine-tuning the Qwen/Qwen2.5-7B-Instruct model on the GAIR/LIMO dataset, focusing on mathematical reasoning tasks. The implementation uses parameter-efficient fine-tuning with LoRA (Low-Rank Adaptation) to efficiently adapt the model while maintaining performance.

## 🚀 Features

- **Efficient Fine-tuning**: Uses LoRA for parameter-efficient training
- **Optimized Data Processing**: Selects 1000 samples with shortest solutions for focused training
- **Memory Efficient**: Supports 4-bit and 8-bit quantization
- **Comprehensive Evaluation**: Built-in evaluation and inference utilities
- **Flexible Configuration**: Easy-to-modify configuration system
- **Monitoring**: Weights & Biases integration for experiment tracking

## 📋 Requirements

- Python 3.8+
- CUDA-compatible GPU (recommended: 16GB+ VRAM)
- PyTorch 2.0+
- Transformers 4.36+

## 🛠️ Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd KULLM-Pro
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. (Optional) Install flash-attention for better performance:
```bash
pip install flash-attn --no-build-isolation
```

## 📊 Dataset

The project uses the [GAIR/LIMO dataset](https://huggingface.co/datasets/GAIR/LIMO), which contains:
- 817 mathematical reasoning problems
- Each sample has: question, detailed solution, and final answer
- Focus on step-by-step mathematical problem solving

The preprocessing script automatically:
- Loads the dataset from Hugging Face
- Selects 1000 samples with the shortest solutions
- Formats data for instruction tuning
- Splits into train/validation sets (90/10)

## 🚀 Quick Start

### Basic Fine-tuning

Run the fine-tuning with default settings:

```bash
python finetune.py
```

### Custom Configuration

Fine-tune with custom parameters:

```bash
python finetune.py \
    --num_samples 1000 \
    --epochs 3 \
    --batch_size 2 \
    --learning_rate 2e-4 \
    --output_dir ./my_model
```

### Data Preprocessing Only

If you want to preprocess data separately:

```bash
python data_preprocessing.py
```

## 📁 Project Structure

```
KULLM-Pro/
├── config.py              # Configuration classes
├── data_preprocessing.py   # Dataset loading and preprocessing
├── training_utils.py       # Model setup and training utilities
├── finetune.py            # Main fine-tuning script
├── evaluate.py            # Evaluation and inference utilities
├── requirements.txt       # Python dependencies
└── README.md             # This file
```

## ⚙️ Configuration

The project uses a hierarchical configuration system defined in `config.py`:

### Model Configuration
- **model_name**: Base model to fine-tune (default: "Qwen/Qwen2.5-7B-Instruct")
- **max_length**: Maximum sequence length (default: 2048)
- **load_in_4bit**: Enable 4-bit quantization for memory efficiency
- **use_flash_attention**: Use Flash Attention 2 for faster training

### LoRA Configuration
- **r**: LoRA rank (default: 16)
- **lora_alpha**: LoRA scaling parameter (default: 32)
- **lora_dropout**: Dropout rate for LoRA layers (default: 0.1)
- **target_modules**: Modules to apply LoRA to

### Training Configuration
- **num_train_epochs**: Number of training epochs (default: 3)
- **per_device_train_batch_size**: Batch size per device (default: 2)
- **learning_rate**: Learning rate (default: 2e-4)
- **gradient_accumulation_steps**: Steps to accumulate gradients (default: 8)

## 🔧 Advanced Usage

### Custom Model Configuration

Modify `config.py` to customize the training:

```python
from config import Config

config = Config()
config.model.max_length = 4096
config.training.num_train_epochs = 5
config.lora.r = 32
```

### Memory Optimization

For limited GPU memory, enable quantization:

```python
config.model.load_in_4bit = True
config.training.gradient_checkpointing = True
config.training.per_device_train_batch_size = 1
config.training.gradient_accumulation_steps = 16
```

### Multi-GPU Training

The training script automatically detects and uses multiple GPUs:

```bash
# Will automatically use all available GPUs
python finetune.py --batch_size 1
```

## 📈 Evaluation

### Evaluate the Fine-tuned Model

```bash
python evaluate.py --model_path ./qwen2.5-7b-limo-finetuned --num_samples 100
```

### Interactive Testing

Test the model interactively:

```bash
python evaluate.py --model_path ./qwen2.5-7b-limo-finetuned --interactive
```

### Evaluation Metrics

The evaluation script provides:
- **Accuracy**: Percentage of correctly solved problems
- **Detailed Results**: Question-by-question analysis
- **Answer Extraction**: Automatic extraction of final answers

## 🎯 Training Tips

### Optimal Hyperparameters

Based on experiments, these settings work well:

```bash
python finetune.py \
    --num_samples 1000 \
    --epochs 3 \
    --batch_size 2 \
    --learning_rate 2e-4 \
    --output_dir ./best_model
```

### Memory Requirements

- **Minimum**: 16GB GPU memory with 4-bit quantization
- **Recommended**: 24GB+ GPU memory for full precision
- **Optimal**: 40GB+ GPU memory for larger batch sizes

### Training Time

- **1000 samples, 3 epochs**: ~2-4 hours on A100
- **Full dataset, 3 epochs**: ~6-10 hours on A100

## 🐛 Troubleshooting

### Common Issues

1. **CUDA Out of Memory**:
   - Reduce batch size: `--batch_size 1`
   - Enable quantization: Set `load_in_4bit = True`
   - Increase gradient accumulation: `gradient_accumulation_steps = 16`

2. **Slow Training**:
   - Install flash-attention: `pip install flash-attn`
   - Use bf16 instead of fp16
   - Increase batch size if memory allows

3. **Poor Performance**:
   - Increase number of samples
   - Adjust learning rate (try 1e-4 or 5e-4)
   - Increase LoRA rank

### Debug Mode

Enable detailed logging:

```bash
export TRANSFORMERS_VERBOSITY=debug
python finetune.py
```

## 📚 References

- [Qwen2.5 Model](https://huggingface.co/Qwen/Qwen2.5-7B-Instruct)
- [GAIR/LIMO Dataset](https://huggingface.co/datasets/GAIR/LIMO)
- [LoRA Paper](https://arxiv.org/abs/2106.09685)
- [PEFT Library](https://github.com/huggingface/peft)

## 📄 License

This project is licensed under the Apache License 2.0 - see the LICENSE file for details.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

If you encounter any issues or have questions, please open an issue on GitHub.