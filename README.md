# Korean-English Code-Switched Mathematical Reasoning

A streamlined pipeline for creating code-switched Korean-English mathematical reasoning data and fine-tuning Qwen2.5-7B-Instruct. Transform English math solutions into Korean-English code-switched versions using OpenAI's o4-mini Batch API, then train and compare models.

## ✨ Key Features

- **🤖 AI-Powered Code-Switching**: OpenAI o4-mini with Batch API (50% cost savings)
- **📊 Dual Model Training**: Compare baseline vs code-switched performance  
- **⚡ Efficient Training**: LoRA fine-tuning with WandB experiment tracking
- **💰 Cost-Effective**: ~$15-50 for 1000 samples (vs $1500+ with o3-pro)
- **🔄 Streamlined Pipeline**: Clean, minimal codebase with essential features only
- **📈 Production Ready**: Robust error handling and progress monitoring

## 🚀 Quick Start

### 1. Setup Environment

```bash
# Clone repository
git clone <repository-url>
cd KULLM-Pro

# Install dependencies
pip install -r requirements.txt

# Set OpenAI API key
export OPENAI_API_KEY="your-api-key-here"
```

### 2. Create Code-Switched Dataset

```bash
# Download 1000 shortest LIMO samples and create code-switched versions
python create_code_switched_data.py --num_samples 1000 --use_batch_api
```

This creates:
- `./data/limo_original_1000.jsonl` - Original English solutions
- `./data/limo_code_switched_1000.jsonl` - Korean-English code-switched versions

### 3. Train Models

#### Option A: Train Both Models (Recommended)

```bash
# Train both baseline and code-switched models
python run_training.py --epochs 3 --batch_size 2
```

#### Option B: Train Individual Models

```bash
# Train baseline model
python train.py \
    --train_file ./data/limo_original_1000_synced.jsonl \
    --output_dir ./models/baseline \
    --epochs 3 --batch_size 2

# Train code-switched model  
python train.py \
    --train_file ./data/limo_code_switched_1000_synced.jsonl \
    --output_dir ./models/code_switched \
    --epochs 3 --batch_size 2
```

## 📁 Project Structure

```
KULLM-Pro/
├── train.py                    # Main training script with WandB support
├── run_training.py            # Script to train both models
├── create_code_switched_data.py # Data creation with Batch API
├── openai_client.py           # OpenAI API client
├── config.yaml               # Configuration file
├── requirements.txt           # Dependencies
├── data/                      # Generated datasets
│   ├── limo_original_1000_synced.jsonl
│   └── limo_code_switched_1000_synced.jsonl
└── models/                    # Trained models
    ├── baseline/
    └── code_switched/
```

## 💰 Cost & Time Estimates

### OpenAI API Costs (o4-mini Batch API)
- **1000 samples**: ~$15-50 total
- **Success rate**: ~79% (794 successful samples)
- **Processing time**: 1-3 hours

### Training Costs (Local GPU)
- **Hardware**: NVIDIA A100-80GB recommended
- **Time per model**: 2-4 hours
- **Total training time**: 4-8 hours for both models

## 🔧 Configuration

### Training Parameters

Key parameters in `train.py`:

```bash
--epochs 3                    # Number of training epochs
--batch_size 2               # Training batch size  
--learning_rate 2e-4         # Learning rate
--lora_r 16                  # LoRA rank
--lora_alpha 32              # LoRA alpha
--wandb_project "project"    # WandB project name
--no_wandb                   # Disable WandB logging
```

### Data Processing Parameters

Key parameters in `create_code_switched_data.py`:

```bash
--num_samples 1000           # Number of samples to process
--use_batch_api             # Use Batch API (recommended)
--batch_size 100            # Batch size for API calls
--output_dir ./data         # Output directory
```

## 📊 Dataset Information

### LIMO Dataset
- **Source**: [GAIR/LIMO](https://huggingface.co/datasets/GAIR/LIMO)
- **Content**: Mathematical reasoning problems with step-by-step solutions
- **Languages**: English → Korean-English code-switched

### Generated Dataset Statistics
- **Original samples**: 1000 requested → ~817 available → 794 synchronized
- **Code-switched samples**: 794 high-quality Korean-English mixed solutions
- **Format**: Conversational (user question + assistant solution)
- **Average length**: 1000-3000 tokens per sample

## 🎯 Model Comparison

The pipeline trains two models for comparison:

1. **Baseline Model**: Trained on original English LIMO data
2. **Code-Switched Model**: Trained on Korean-English code-switched data

Both models use:
- **Base model**: Qwen/Qwen2.5-7B-Instruct
- **Fine-tuning**: LoRA (Low-Rank Adaptation)
- **Training data**: 794 synchronized mathematical reasoning samples

## 📈 Experiment Tracking

The pipeline includes WandB integration for experiment tracking:

- **Automatic logging**: Training metrics, hyperparameters, model artifacts
- **Run naming**: Auto-generated based on dataset type and timestamp
- **Project organization**: Separate runs for baseline vs code-switched models
- **Comparison**: Easy comparison of model performance

## 🔍 Key Files

### Core Scripts
- `train.py` - Streamlined training script with WandB support
- `run_training.py` - Orchestrates training of both models
- `create_code_switched_data.py` - Creates code-switched dataset using OpenAI API

### Configuration
- `config.yaml` - Centralized configuration for easy parameter management
- `requirements.txt` - All necessary Python dependencies

### Documentation
- `COSTS.md` - Detailed cost and time estimates
- `README.md` - This file

## 🚨 Important Notes

1. **API Costs**: Monitor your OpenAI usage dashboard during data creation
2. **GPU Memory**: Training requires ~40-60GB VRAM (A100-80GB recommended)
3. **Data Synchronization**: The pipeline automatically synchronizes datasets for fair comparison
4. **Success Rate**: ~79% success rate is normal due to OpenAI content policy filters

## 🤝 Contributing

This is a research project focused on Korean-English code-switching in mathematical reasoning. The codebase has been streamlined for clarity and ease of use.

## 📄 License

See LICENSE file for details.
