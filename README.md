# KULLM-Pro: Code-Switching Fine-tuning Pipeline

This project provides a complete pipeline for creating **code-switched Korean-English mathematical reasoning data** and fine-tuning the Qwen/Qwen2.5-7B-Instruct model. The pipeline uses OpenAI's o1-mini model to transform English mathematical solutions into sophisticated Korean-English code-switched versions, then trains two models for comparison: baseline (original) and code-switched.

## 🚀 Key Features

- **AI-Powered Code-Switching**: Uses OpenAI o1-mini to create sophisticated Korean-English code-switched mathematical solutions
- **Dual Model Training**: Trains both baseline and code-switched models for comparison
- **Efficient Fine-tuning**: Uses LoRA for parameter-efficient training
- **JSONL Pipeline**: Works with JSONL format for flexible data handling
- **Memory Efficient**: Supports 4-bit and 8-bit quantization
- **Production Ready**: Comprehensive error handling, rate limiting, and progress tracking
- **Cost Monitoring**: Built-in OpenAI API usage and cost estimation

## 📋 Requirements

- Python 3.8+
- OpenAI API key (for code-switching)
- CUDA-compatible GPU (recommended: 16GB+ VRAM)
- PyTorch 2.0+
- Transformers 4.36+

## 🛠️ Installation

1. Clone the repository and install dependencies:

```bash
git clone <repository-url>
cd KULLM-Pro
pip install -r requirements.txt
```

2. Set up your OpenAI API key:

```bash
export OPENAI_API_KEY="your-api-key-here"
```

3. (Optional) Install flash-attention for better performance:

```bash
pip install flash-attn --no-build-isolation
```

## 📊 Dataset

The project uses the [GAIR/LIMO dataset](https://huggingface.co/datasets/GAIR/LIMO), which contains:
- 817 mathematical reasoning problems
- Each sample has: question, detailed solution, and final answer
- Focus on step-by-step mathematical problem solving

The preprocessing script automatically:
- Loads the dataset from Hugging Face
- Selects 1000 samples with the shortest solutions
- Formats data for instruction tuning
- Splits into train/validation sets (90/10)

## 🚀 Quick Start

### Interactive Quick Start

Run the interactive setup (recommended for first-time users):

```bash
python quick_start.py
```

⚠️ **IMPORTANT**: The pipeline now uses OpenAI's o3-pro model, which is significantly more expensive and slower than o1-mini. Start with small samples!

### Complete Pipeline (Data + Training)

Create code-switched data and train both models:

```bash
# Test with 5 samples first (~$8-25, 10-25 minutes)
python run_pipeline.py --num_samples 5

# Medium test: 50 samples (~$400-1,250, 2-4 hours)
python run_pipeline.py --num_samples 50

# Full pipeline: 1000 samples (~$1,500-5,000, 2-3 days)
python run_pipeline.py --num_samples 1000
```

### Step-by-Step Process

1. **Create code-switched dataset**:

```bash
python create_code_switched_data.py --num_samples 1000
```

2. **Train baseline model** (original LIMO data):

```bash
python finetune.py --train_file ./data/limo_original_1000.jsonl --output_dir ./models/baseline
```

3. **Train code-switched model**:

```bash
python finetune.py --train_file ./data/limo_code_switched_1000.jsonl --output_dir ./models/code_switched
```

## 📁 Project Structure

```text
KULLM-Pro/
├── config.py                    # Configuration classes
├── openai_client.py            # OpenAI API integration
├── create_code_switched_data.py # Main code-switching script
├── jsonl_data_processor.py     # JSONL data handling
├── training_utils.py           # Model setup utilities
├── finetune.py                 # Fine-tuning script
├── run_pipeline.py             # Complete pipeline runner
├── quick_start.py              # Interactive quick start
├── system_prompt.txt           # Code-switching system prompt
├── requirements.txt            # Python dependencies
└── README.md                   # Documentation
```

## 🔄 Code-Switching Process

The pipeline uses a sophisticated system prompt to transform English mathematical solutions into Korean-English code-switched versions:

1. **Conceptual Efficiency**: Uses the most precise language for each concept
2. **Linguistic Structure**: Adapts grammar for natural flow
3. **Strategic Switching**: Intelligent term-level and sentence-level switching
4. **Completeness**: Maintains all logical steps and reasoning

Example transformation:
- **Original**: "To find the prime factorization, we need to divide by small primes..."
- **Code-switched**: "Prime factorization을 찾기 위해서는 작은 소수들로 나누어야 한다..."

## ⚙️ Configuration

The project uses a hierarchical configuration system defined in `config.py`:

### Model Configuration
- **model_name**: Base model to fine-tune (default: "Qwen/Qwen2.5-7B-Instruct")
- **max_length**: Maximum sequence length (default: 2048)
- **load_in_4bit**: Enable 4-bit quantization for memory efficiency
- **use_flash_attention**: Use Flash Attention 2 for faster training

### LoRA Configuration
- **r**: LoRA rank (default: 16)
- **lora_alpha**: LoRA scaling parameter (default: 32)
- **lora_dropout**: Dropout rate for LoRA layers (default: 0.1)
- **target_modules**: Modules to apply LoRA to

### Training Configuration
- **num_train_epochs**: Number of training epochs (default: 3)
- **per_device_train_batch_size**: Batch size per device (default: 2)
- **learning_rate**: Learning rate (default: 2e-4)
- **gradient_accumulation_steps**: Steps to accumulate gradients (default: 8)

## 🔧 Advanced Usage

### Custom Model Configuration

Modify `config.py` to customize the training:

```python
from config import Config

config = Config()
config.model.max_length = 4096
config.training.num_train_epochs = 5
config.lora.r = 32
```

### Memory Optimization

For limited GPU memory, enable quantization:

```python
config.model.load_in_4bit = True
config.training.gradient_checkpointing = True
config.training.per_device_train_batch_size = 1
config.training.gradient_accumulation_steps = 16
```

### Multi-GPU Training

The training script automatically detects and uses multiple GPUs:

```bash
# Will automatically use all available GPUs
python finetune.py --batch_size 1
```

## 📈 Evaluation

### Evaluate the Fine-tuned Model

```bash
python evaluate.py --model_path ./qwen2.5-7b-limo-finetuned --num_samples 100
```

### Interactive Testing

Test the model interactively:

```bash
python evaluate.py --model_path ./qwen2.5-7b-limo-finetuned --interactive
```

### Evaluation Metrics

The evaluation script provides:
- **Accuracy**: Percentage of correctly solved problems
- **Detailed Results**: Question-by-question analysis
- **Answer Extraction**: Automatic extraction of final answers

## 🎯 Training Tips

### Optimal Hyperparameters

Based on experiments, these settings work well:

```bash
python finetune.py \
    --num_samples 1000 \
    --epochs 3 \
    --batch_size 2 \
    --learning_rate 2e-4 \
    --output_dir ./best_model
```

### Memory Requirements

- **Minimum**: 16GB GPU memory with 4-bit quantization
- **Recommended**: 24GB+ GPU memory for full precision
- **Optimal**: 40GB+ GPU memory for larger batch sizes

### Training Time

- **1000 samples, 3 epochs**: ~2-4 hours on A100
- **Full dataset, 3 epochs**: ~6-10 hours on A100

## 🐛 Troubleshooting

### Common Issues

1. **CUDA Out of Memory**:
   - Reduce batch size: `--batch_size 1`
   - Enable quantization: Set `load_in_4bit = True`
   - Increase gradient accumulation: `gradient_accumulation_steps = 16`

2. **Slow Training**:
   - Install flash-attention: `pip install flash-attn`
   - Use bf16 instead of fp16
   - Increase batch size if memory allows

3. **Poor Performance**:
   - Increase number of samples
   - Adjust learning rate (try 1e-4 or 5e-4)
   - Increase LoRA rank

### Debug Mode

Enable detailed logging:

```bash
export TRANSFORMERS_VERBOSITY=debug
python finetune.py
```

## 📚 References

- [Qwen2.5 Model](https://huggingface.co/Qwen/Qwen2.5-7B-Instruct)
- [GAIR/LIMO Dataset](https://huggingface.co/datasets/GAIR/LIMO)
- [LoRA Paper](https://arxiv.org/abs/2106.09685)
- [PEFT Library](https://github.com/huggingface/peft)

## 📄 License

This project is licensed under the Apache License 2.0 - see the LICENSE file for details.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

If you encounter any issues or have questions, please open an issue on GitHub.