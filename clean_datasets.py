"""
Script to remove failed samples from both original and code-switched datasets
to ensure fair comparison with identical data
"""

import json
import logging
import argparse
import os
from typing import Set, List, Dict, Any

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def extract_failed_indices(error_file: str) -> Set[int]:
    """Extract the indices of failed samples from error.jsonl"""
    failed_indices = set()
    
    if not os.path.exists(error_file):
        logger.warning(f"Error file {error_file} not found. No samples to remove.")
        return failed_indices
    
    with open(error_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            
            try:
                error_data = json.loads(line)
                custom_id = error_data.get('custom_id', '')
                
                # Extract index from custom_id like "request-10" -> 10
                if custom_id.startswith('request-'):
                    index = int(custom_id.split('-')[1])
                    failed_indices.add(index)
                    logger.info(f"Found failed sample at index: {index}")
                    
            except (json.JSONDecodeError, ValueError, IndexError) as e:
                logger.warning(f"Could not parse error line: {line[:100]}... Error: {e}")
                continue
    
    logger.info(f"Total failed samples found: {len(failed_indices)}")
    return failed_indices

def load_jsonl(file_path: str) -> List[Dict[str, Any]]:
    """Load data from JSONL file"""
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line:
                data.append(json.loads(line))
    return data

def save_jsonl(data: List[Dict[str, Any]], file_path: str):
    """Save data to JSONL file"""
    with open(file_path, 'w', encoding='utf-8') as f:
        for item in data:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')

def clean_dataset(input_file: str, output_file: str, failed_indices: Set[int]) -> int:
    """Remove failed samples from a dataset"""
    logger.info(f"Cleaning dataset: {input_file}")
    
    # Load original data
    data = load_jsonl(input_file)
    original_count = len(data)
    
    # Remove failed samples (indices are 0-based)
    cleaned_data = []
    removed_count = 0
    
    for i, item in enumerate(data):
        if i in failed_indices:
            removed_count += 1
            logger.debug(f"Removing sample at index {i}")
        else:
            cleaned_data.append(item)
    
    # Save cleaned data
    save_jsonl(cleaned_data, output_file)
    
    logger.info(f"Original samples: {original_count}")
    logger.info(f"Removed samples: {removed_count}")
    logger.info(f"Remaining samples: {len(cleaned_data)}")
    logger.info(f"Cleaned dataset saved to: {output_file}")
    
    return len(cleaned_data)

def main():
    parser = argparse.ArgumentParser(description="Clean datasets by removing failed samples")
    parser.add_argument("--error_file", type=str, default="./data/error.jsonl", 
                       help="Path to error.jsonl file")
    parser.add_argument("--original_file", type=str, default="./data/limo_original_1000.jsonl",
                       help="Path to original dataset file")
    parser.add_argument("--code_switched_file", type=str, default="./data/limo_code_switched_1000.jsonl",
                       help="Path to code-switched dataset file")
    parser.add_argument("--output_dir", type=str, default="./data",
                       help="Output directory for cleaned files")
    parser.add_argument("--suffix", type=str, default="_cleaned",
                       help="Suffix to add to cleaned filenames")
    
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Extract failed indices
    failed_indices = extract_failed_indices(args.error_file)
    
    if not failed_indices:
        logger.info("No failed samples found. No cleaning needed.")
        return
    
    # Generate output filenames
    original_basename = os.path.splitext(os.path.basename(args.original_file))[0]
    code_switched_basename = os.path.splitext(os.path.basename(args.code_switched_file))[0]
    
    cleaned_original_file = os.path.join(args.output_dir, f"{original_basename}{args.suffix}.jsonl")
    cleaned_code_switched_file = os.path.join(args.output_dir, f"{code_switched_basename}{args.suffix}.jsonl")
    
    # Clean both datasets
    logger.info("=" * 60)
    logger.info("CLEANING DATASETS")
    logger.info("=" * 60)
    
    # Clean original dataset
    original_count = clean_dataset(args.original_file, cleaned_original_file, failed_indices)
    
    print()  # Add spacing
    
    # Clean code-switched dataset
    code_switched_count = clean_dataset(args.code_switched_file, cleaned_code_switched_file, failed_indices)
    
    # Verify both datasets have the same number of samples
    logger.info("=" * 60)
    logger.info("VERIFICATION")
    logger.info("=" * 60)
    
    if original_count == code_switched_count:
        logger.info(f"✅ SUCCESS: Both datasets have {original_count} samples")
        logger.info(f"✅ Datasets are now synchronized for fair comparison")
    else:
        logger.error(f"❌ ERROR: Sample count mismatch!")
        logger.error(f"   Original: {original_count} samples")
        logger.error(f"   Code-switched: {code_switched_count} samples")
        return
    
    # Summary
    logger.info("=" * 60)
    logger.info("SUMMARY")
    logger.info("=" * 60)
    logger.info(f"Failed samples removed: {len(failed_indices)}")
    logger.info(f"Remaining samples: {original_count}")
    logger.info(f"Success rate: {original_count / (original_count + len(failed_indices)) * 100:.1f}%")
    logger.info("")
    logger.info("Cleaned files:")
    logger.info(f"  Original: {cleaned_original_file}")
    logger.info(f"  Code-switched: {cleaned_code_switched_file}")
    logger.info("")
    logger.info("Next steps:")
    logger.info("  1. Use cleaned files for training:")
    logger.info(f"     python finetune.py --train_file {cleaned_original_file} --output_dir ./models/baseline_cleaned")
    logger.info(f"     python finetune.py --train_file {cleaned_code_switched_file} --output_dir ./models/code_switched_cleaned")

if __name__ == "__main__":
    main()
