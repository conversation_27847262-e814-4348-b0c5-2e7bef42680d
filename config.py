"""
Configuration file for fine-tuning Qwen2.5-7B-Instruct on GAIR/LIMO dataset
"""

import os
from dataclasses import dataclass
from typing import Optional


@dataclass
class ModelConfig:
    """Model configuration"""

    model_name: str = "Qwen/Qwen2.5-7B-Instruct"
    max_length: int = 2048
    use_flash_attention: bool = False  # Disabled to avoid installation issues
    load_in_4bit: bool = False  # Disabled by default for compatibility
    load_in_8bit: bool = False
    torch_dtype: str = "auto"


@dataclass
class DataConfig:
    """Data configuration"""

    # For JSONL files
    train_file: str = "./data/limo_original_1000.jsonl"
    val_file: Optional[str] = None  # If None, will split from train_file
    test_size: float = 0.1
    random_seed: int = 42

    # Legacy HuggingFace dataset support (optional)
    dataset_name: Optional[str] = None
    dataset_split: str = "train"
    num_samples: int = 1000


@dataclass
class LoRAConfig:
    """LoRA configuration for parameter-efficient fine-tuning"""

    r: int = 16
    lora_alpha: int = 32
    lora_dropout: float = 0.1
    bias: str = "none"
    task_type: str = "CAUSAL_LM"
    target_modules: list = None

    def __post_init__(self):
        if self.target_modules is None:
            # Default target modules for Qwen2.5
            self.target_modules = [
                "q_proj",
                "k_proj",
                "v_proj",
                "o_proj",
                "gate_proj",
                "up_proj",
                "down_proj",
            ]


@dataclass
class TrainingConfig:
    """Training configuration"""

    output_dir: str = "./qwen2.5-7b-limo-finetuned"
    num_train_epochs: int = 3
    per_device_train_batch_size: int = 2
    per_device_eval_batch_size: int = 4
    gradient_accumulation_steps: int = 8
    learning_rate: float = 2e-4
    weight_decay: float = 0.01
    warmup_ratio: float = 0.1
    lr_scheduler_type: str = "cosine"
    logging_steps: int = 10
    eval_steps: int = 100
    save_steps: int = 500
    save_total_limit: int = 3
    evaluation_strategy: str = "steps"
    save_strategy: str = "steps"
    load_best_model_at_end: bool = True
    metric_for_best_model: str = "eval_loss"
    greater_is_better: bool = False
    report_to: str = "wandb"
    run_name: str = "qwen2.5-7b-limo-finetune"
    dataloader_num_workers: int = 4
    fp16: bool = False
    bf16: bool = True
    gradient_checkpointing: bool = True
    optim: str = "adamw_torch"
    group_by_length: bool = True
    remove_unused_columns: bool = False


@dataclass
class Config:
    """Main configuration class"""

    model: ModelConfig = ModelConfig()
    data: DataConfig = DataConfig()
    lora: LoRAConfig = LoRAConfig()
    training: TrainingConfig = TrainingConfig()

    # Environment settings
    cache_dir: str = "./cache"
    log_level: str = "INFO"

    def __post_init__(self):
        # Create necessary directories
        os.makedirs(self.training.output_dir, exist_ok=True)
        os.makedirs(self.cache_dir, exist_ok=True)
