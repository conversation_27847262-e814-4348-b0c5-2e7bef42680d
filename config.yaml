# Configuration for Korean-English Code-Switched Mathematical Reasoning Training
# Qwen2.5-7B-Instruct fine-tuning on LIMO dataset

# Model Configuration
model:
  name: "Qwen/Qwen2.5-7B-Instruct"
  max_length: 2048

# Training Configuration
training:
  epochs: 3
  batch_size: 2
  learning_rate: 2e-4
  gradient_accumulation_steps: 8
  weight_decay: 0.01
  warmup_ratio: 0.1
  lr_scheduler_type: "cosine"
  
  # Evaluation and Saving
  eval_steps: 100
  save_steps: 500
  logging_steps: 10
  save_total_limit: 3
  load_best_model_at_end: true
  metric_for_best_model: "eval_loss"
  
  # Performance Optimizations
  bf16: true
  gradient_checkpointing: true
  dataloader_num_workers: 0
  optim: "adamw_torch"

# LoRA Configuration
lora:
  r: 16
  alpha: 32
  dropout: 0.1
  target_modules:
    - "q_proj"
    - "k_proj" 
    - "v_proj"
    - "o_proj"
    - "gate_proj"
    - "up_proj"
    - "down_proj"

# Experiment Tracking
wandb:
  project: "qwen-code-switched-math"
  enabled: true

# Data Configuration
data:
  train_split_ratio: 0.9
  val_split_ratio: 0.1

# Paths (will be overridden by command line arguments)
paths:
  baseline_data: "./data/limo_original_1000_synced.jsonl"
  code_switched_data: "./data/limo_code_switched_1000_synced.jsonl"
  baseline_output: "./models/baseline"
  code_switched_output: "./models/code_switched"
