"""
Main script to create code-switched LIMO dataset using OpenAI o1-mini
"""

import os
import json
import asyncio
import logging
import argparse
from typing import List, Dict, Any
from datetime import datetime

import pandas as pd
from datasets import load_dataset
from tqdm import tqdm

from openai_client import OpenAIConfig, CodeSwitchingProcessor

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("code_switching.log"), logging.StreamHandler()],
)
logger = logging.getLogger(__name__)


class LIMODataProcessor:
    """Processor for LIMO dataset"""

    def __init__(self, num_samples: int = 1000):
        self.num_samples = num_samples
        self.dataset = None

    def download_and_filter_limo(self) -> List[Dict[str, Any]]:
        """Download LIMO dataset and filter for shortest solutions"""
        logger.info("Downloading GAIR/LIMO dataset...")

        try:
            # Load the dataset
            dataset = load_dataset("GAIR/LIMO", split="train")
            logger.info(f"Downloaded {len(dataset)} samples from GAIR/LIMO")

            # Convert to pandas for easier manipulation
            df = dataset.to_pandas()

            # Calculate solution lengths
            df["solution_length"] = df["solution"].str.len()

            # Sort by solution length and select shortest ones
            df_sorted = df.sort_values("solution_length").head(self.num_samples)

            logger.info(f"Selected {len(df_sorted)} samples with shortest solutions")
            logger.info(
                f"Solution length range: {df_sorted['solution_length'].min()} - {df_sorted['solution_length'].max()}"
            )

            # Convert to list of dictionaries
            selected_data = df_sorted[["question", "solution", "answer"]].to_dict(
                "records"
            )

            return selected_data

        except Exception as e:
            logger.error(f"Error downloading/filtering LIMO dataset: {e}")
            raise

    def save_original_data(
        self, data: List[Dict[str, Any]], filename: str = "limo_original_1000.jsonl"
    ):
        """Save original LIMO data as JSONL"""
        logger.info(f"Saving original data to {filename}")

        with open(filename, "w", encoding="utf-8") as f:
            for item in data:
                # Format for training
                training_item = {
                    "messages": [
                        {
                            "role": "system",
                            "content": "You are a helpful assistant that solves mathematical problems step by step.",
                        },
                        {
                            "role": "user",
                            "content": f"Solve the following mathematical problem step by step. Provide a detailed solution and then give the final answer.\n\nProblem: {item['question']}",
                        },
                        {
                            "role": "assistant",
                            "content": f"{item['solution']}\n\nFinal Answer: {item['answer']}",
                        },
                    ]
                }
                f.write(json.dumps(training_item, ensure_ascii=False) + "\n")

        logger.info(f"Saved {len(data)} original samples to {filename}")


async def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Create code-switched LIMO dataset")
    parser.add_argument(
        "--num_samples", type=int, default=1000, help="Number of samples to process"
    )
    parser.add_argument(
        "--api_key", type=str, help="OpenAI API key (or set OPENAI_API_KEY env var)"
    )
    parser.add_argument(
        "--model", type=str, default="o3-pro-2025-06-10", help="OpenAI model to use"
    )
    parser.add_argument(
        "--max_concurrent", type=int, default=2, help="Max concurrent requests"
    )
    parser.add_argument(
        "--requests_per_minute", type=int, default=10, help="Requests per minute limit"
    )
    parser.add_argument(
        "--system_prompt",
        type=str,
        default="system_prompt.txt",
        help="Path to system prompt file",
    )
    parser.add_argument(
        "--output_dir", type=str, default="./data", help="Output directory"
    )
    parser.add_argument(
        "--skip_download",
        action="store_true",
        help="Skip downloading, use existing data",
    )
    parser.add_argument(
        "--original_data_file", type=str, help="Path to existing original data file"
    )

    args = parser.parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Set up file paths
    original_file = os.path.join(args.output_dir, "limo_original_1000.jsonl")
    code_switched_file = os.path.join(args.output_dir, "limo_code_switched_1000.jsonl")

    # Step 1: Download and prepare original data
    if not args.skip_download:
        logger.info("=== Step 1: Downloading and preparing original LIMO data ===")

        processor = LIMODataProcessor(args.num_samples)
        original_data = processor.download_and_filter_limo()

        # Save original data as JSONL
        processor.save_original_data(original_data, original_file)

    else:
        logger.info("=== Step 1: Loading existing original data ===")
        if args.original_data_file and os.path.exists(args.original_data_file):
            original_file = args.original_data_file

        # Load original data
        original_data = []
        with open(original_file, "r", encoding="utf-8") as f:
            for line in f:
                item = json.loads(line)
                # Extract from messages format
                user_msg = item["messages"][1]["content"]
                assistant_msg = item["messages"][2]["content"]

                # Parse question from user message
                question = user_msg.split("Problem: ")[-1]

                # Parse solution and answer from assistant message
                parts = assistant_msg.split("\n\nFinal Answer: ")
                solution = parts[0]
                answer = parts[1] if len(parts) > 1 else ""

                original_data.append(
                    {"question": question, "solution": solution, "answer": answer}
                )

        logger.info(f"Loaded {len(original_data)} samples from {original_file}")

    # Step 2: Create code-switched version using OpenAI
    logger.info("=== Step 2: Creating code-switched version using OpenAI ===")

    # Set up OpenAI configuration
    openai_config = OpenAIConfig(
        api_key=args.api_key,
        model=args.model,
        max_concurrent_requests=args.max_concurrent,
        requests_per_minute=args.requests_per_minute,
    )

    # Initialize code-switching processor
    code_switcher = CodeSwitchingProcessor(openai_config, args.system_prompt)

    # Process the data
    try:
        code_switched_results, failed_items = (
            await code_switcher.process_limo_solutions(original_data)
        )

        # Save code-switched data as JSONL
        logger.info(f"Saving code-switched data to {code_switched_file}")

        with open(code_switched_file, "w", encoding="utf-8") as f:
            for item in code_switched_results:
                # Format for training
                training_item = {
                    "messages": [
                        {
                            "role": "system",
                            "content": "You are a helpful assistant that solves mathematical problems step by step.",
                        },
                        {
                            "role": "user",
                            "content": f"Solve the following mathematical problem step by step. Provide a detailed solution and then give the final answer.\n\nProblem: {item['question']}",
                        },
                        {
                            "role": "assistant",
                            "content": f"{item['code_switched_solution']}\n\nFinal Answer: {item['answer']}",
                        },
                    ],
                    "metadata": {
                        "original_solution": item["original_solution"],
                        "processing_info": item["processing_info"],
                    },
                }
                f.write(json.dumps(training_item, ensure_ascii=False) + "\n")

        # Save failed items if any
        if failed_items:
            failed_file = os.path.join(args.output_dir, "failed_items.json")
            with open(failed_file, "w", encoding="utf-8") as f:
                json.dump(failed_items, f, ensure_ascii=False, indent=2)
            logger.warning(f"Saved {len(failed_items)} failed items to {failed_file}")

        # Save processing statistics
        stats = code_switcher.client.get_usage_stats()
        stats_file = os.path.join(args.output_dir, "processing_stats.json")
        with open(stats_file, "w", encoding="utf-8") as f:
            json.dump(
                {
                    "timestamp": datetime.now().isoformat(),
                    "args": vars(args),
                    "api_stats": stats,
                    "results": {
                        "total_input": len(original_data),
                        "successful": len(code_switched_results),
                        "failed": len(failed_items),
                        "success_rate": len(code_switched_results) / len(original_data),
                    },
                },
                f,
                indent=2,
            )

        logger.info("=== Processing Complete ===")
        logger.info(f"Original data: {original_file}")
        logger.info(f"Code-switched data: {code_switched_file}")
        logger.info(
            f"Successfully processed: {len(code_switched_results)}/{len(original_data)}"
        )
        logger.info(f"API usage stats: {stats}")

        # Show sample of code-switched data
        if code_switched_results:
            logger.info("\n=== Sample Code-Switched Result ===")
            sample = code_switched_results[0]
            logger.info(f"Question: {sample['question'][:200]}...")
            logger.info(f"Original solution: {sample['original_solution'][:200]}...")
            logger.info(
                f"Code-switched solution: {sample['code_switched_solution'][:200]}..."
            )

    except Exception as e:
        logger.error(f"Error during code-switching processing: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
