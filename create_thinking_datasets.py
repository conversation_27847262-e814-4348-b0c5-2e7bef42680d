#!/usr/bin/env python3
"""
Script to create thinking model datasets from existing synchronized data
"""

import subprocess
import logging
import os

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_command(command, description=""):
    """Run a shell command and handle errors"""
    logger.info(f"Running: {description or command}")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            text=True,
            capture_output=True
        )
        
        logger.info(f"✅ {description} completed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ {description} failed with return code {e.returncode}")
        logger.error(f"Error output: {e.stderr}")
        return False

def main():
    """Main function to create thinking datasets"""
    
    # Input files (synchronized datasets)
    baseline_input = "./data/limo_original_1000_synced.jsonl"
    code_switched_input = "./data/limo_code_switched_1000_synced.jsonl"
    
    # Output files (thinking format)
    baseline_output = "./data/limo_original_1000_thinking.jsonl"
    code_switched_output = "./data/limo_code_switched_1000_thinking.jsonl"
    
    logger.info("=" * 60)
    logger.info("CREATING THINKING MODEL DATASETS")
    logger.info("=" * 60)
    
    # Check if input files exist
    for file_path in [baseline_input, code_switched_input]:
        if not os.path.exists(file_path):
            logger.error(f"Input file not found: {file_path}")
            return False
    
    success_count = 0
    
    # Process baseline dataset
    logger.info("1. Processing baseline dataset...")
    baseline_cmd = f"python format_thinking_data.py --input_file {baseline_input} --output_file {baseline_output}"
    if run_command(baseline_cmd, "Baseline dataset formatting"):
        success_count += 1
        logger.info(f"✅ Baseline thinking dataset created: {baseline_output}")
    
    # Process code-switched dataset  
    logger.info("2. Processing code-switched dataset...")
    code_switched_cmd = f"python format_thinking_data.py --input_file {code_switched_input} --output_file {code_switched_output}"
    if run_command(code_switched_cmd, "Code-switched dataset formatting"):
        success_count += 1
        logger.info(f"✅ Code-switched thinking dataset created: {code_switched_output}")
    
    # Summary
    logger.info("=" * 60)
    logger.info("SUMMARY")
    logger.info("=" * 60)
    
    if success_count == 2:
        logger.info("🎉 All thinking datasets created successfully!")
        logger.info("")
        logger.info("Created files:")
        logger.info(f"  📁 Baseline (thinking): {baseline_output}")
        logger.info(f"  📁 Code-switched (thinking): {code_switched_output}")
        logger.info("")
        logger.info("Next steps for training thinking models:")
        logger.info("  1. Train baseline thinking model:")
        logger.info(f"     python train.py --train_file {baseline_output} --output_dir ./models/baseline_thinking")
        logger.info("  2. Train code-switched thinking model:")
        logger.info(f"     python train.py --train_file {code_switched_output} --output_dir ./models/code_switched_thinking")
        logger.info("")
        logger.info("Or train both at once:")
        logger.info("     python run_thinking_training.py")
    else:
        logger.error(f"❌ Only {success_count}/2 datasets processed successfully")
        return False
    
    return True

if __name__ == "__main__":
    main()
