"""
Data preprocessing script for GAIR/LIMO dataset
Loads the dataset, selects 1000 samples with shortest solutions, and formats for instruction tuning
"""

import logging
import pandas as pd
from datasets import load_dataset, Dataset
from sklearn.model_selection import train_test_split
from typing import Dict, List, Tuple
import json
import os

from config import Config

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LIMODataProcessor:
    """Data processor for GAIR/LIMO dataset"""
    
    def __init__(self, config: Config):
        self.config = config
        self.dataset = None
        self.processed_data = None
        
    def load_dataset(self) -> None:
        """Load the GAIR/LIMO dataset"""
        logger.info(f"Loading dataset: {self.config.data.dataset_name}")
        
        try:
            self.dataset = load_dataset(
                self.config.data.dataset_name,
                split=self.config.data.dataset_split,
                cache_dir=self.config.cache_dir
            )
            logger.info(f"Dataset loaded successfully. Total samples: {len(self.dataset)}")
            
        except Exception as e:
            logger.error(f"Error loading dataset: {e}")
            raise
    
    def select_shortest_solutions(self, num_samples: int = 1000) -> Dataset:
        """Select samples with the shortest solutions"""
        logger.info(f"Selecting {num_samples} samples with shortest solutions")
        
        # Convert to pandas for easier manipulation
        df = self.dataset.to_pandas()
        
        # Calculate solution lengths
        df['solution_length'] = df['solution'].str.len()
        
        # Sort by solution length and select top num_samples
        df_sorted = df.sort_values('solution_length').head(num_samples)
        
        logger.info(f"Selected {len(df_sorted)} samples")
        logger.info(f"Solution length range: {df_sorted['solution_length'].min()} - {df_sorted['solution_length'].max()}")
        
        # Convert back to Dataset
        selected_dataset = Dataset.from_pandas(df_sorted.drop('solution_length', axis=1))
        
        return selected_dataset
    
    def format_for_instruction_tuning(self, dataset: Dataset) -> Dataset:
        """Format the dataset for instruction tuning"""
        logger.info("Formatting dataset for instruction tuning")
        
        def format_sample(example):
            """Format a single sample for instruction tuning"""
            # Create instruction-following format
            instruction = "Solve the following mathematical problem step by step. Provide a detailed solution and then give the final answer."
            
            # Format the conversation
            messages = [
                {
                    "role": "system",
                    "content": "You are a helpful assistant that solves mathematical problems step by step."
                },
                {
                    "role": "user", 
                    "content": f"{instruction}\n\nProblem: {example['question']}"
                },
                {
                    "role": "assistant",
                    "content": f"{example['solution']}\n\nFinal Answer: {example['answer']}"
                }
            ]
            
            return {
                "messages": messages,
                "question": example['question'],
                "solution": example['solution'],
                "answer": example['answer']
            }
        
        formatted_dataset = dataset.map(format_sample)
        
        logger.info("Dataset formatting completed")
        return formatted_dataset
    
    def create_chat_template(self, example):
        """Apply chat template to format the conversation"""
        # This will be used with the tokenizer's chat template
        return example
    
    def split_dataset(self, dataset: Dataset) -> Tuple[Dataset, Dataset]:
        """Split dataset into train and validation sets"""
        logger.info(f"Splitting dataset with test_size={self.config.data.test_size}")
        
        # Convert to list for splitting
        data_list = list(dataset)
        
        train_data, val_data = train_test_split(
            data_list,
            test_size=self.config.data.test_size,
            random_state=self.config.data.random_seed,
            shuffle=True
        )
        
        train_dataset = Dataset.from_list(train_data)
        val_dataset = Dataset.from_list(val_data)
        
        logger.info(f"Train samples: {len(train_dataset)}")
        logger.info(f"Validation samples: {len(val_dataset)}")
        
        return train_dataset, val_dataset
    
    def save_processed_data(self, train_dataset: Dataset, val_dataset: Dataset) -> None:
        """Save processed datasets"""
        output_dir = os.path.join(self.config.cache_dir, "processed_data")
        os.makedirs(output_dir, exist_ok=True)
        
        train_path = os.path.join(output_dir, "train_dataset")
        val_path = os.path.join(output_dir, "val_dataset")
        
        train_dataset.save_to_disk(train_path)
        val_dataset.save_to_disk(val_path)
        
        logger.info(f"Processed datasets saved to {output_dir}")
        
        # Also save some statistics
        stats = {
            "total_samples": len(train_dataset) + len(val_dataset),
            "train_samples": len(train_dataset),
            "val_samples": len(val_dataset),
            "test_size": self.config.data.test_size,
            "random_seed": self.config.data.random_seed
        }
        
        with open(os.path.join(output_dir, "dataset_stats.json"), "w") as f:
            json.dump(stats, f, indent=2)
    
    def process_data(self) -> Tuple[Dataset, Dataset]:
        """Main data processing pipeline"""
        logger.info("Starting data processing pipeline")
        
        # Load dataset
        self.load_dataset()
        
        # Select shortest solutions
        selected_dataset = self.select_shortest_solutions(self.config.data.num_samples)
        
        # Format for instruction tuning
        formatted_dataset = self.format_for_instruction_tuning(selected_dataset)
        
        # Split into train/val
        train_dataset, val_dataset = self.split_dataset(formatted_dataset)
        
        # Save processed data
        self.save_processed_data(train_dataset, val_dataset)
        
        logger.info("Data processing pipeline completed")
        
        return train_dataset, val_dataset

def main():
    """Main function for testing data preprocessing"""
    config = Config()
    processor = LIMODataProcessor(config)
    
    train_dataset, val_dataset = processor.process_data()
    
    # Print sample data
    print("\n" + "="*50)
    print("SAMPLE TRAINING DATA:")
    print("="*50)
    sample = train_dataset[0]
    print(f"Question: {sample['question'][:200]}...")
    print(f"Solution: {sample['solution'][:300]}...")
    print(f"Answer: {sample['answer']}")
    print(f"Messages format: {len(sample['messages'])} messages")

if __name__ == "__main__":
    main()
