"""
Evaluation and inference utilities for the fine-tuned Qwen2.5-7B-Instruct model
"""

import os
import logging
import torch
import json
import argparse
from typing import List, Dict, Any
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel
from datasets import load_dataset
import re

from config import Config

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelInference:
    """Handles model inference and evaluation"""
    
    def __init__(self, model_path: str, base_model_name: str = None):
        self.model_path = model_path
        self.base_model_name = base_model_name or "Qwen/Qwen2.5-7B-Instruct"
        self.model = None
        self.tokenizer = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
    def load_model(self):
        """Load the fine-tuned model and tokenizer"""
        logger.info(f"Loading model from {self.model_path}")
        
        # Load tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(
            self.model_path,
            trust_remote_code=True
        )
        
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        # Check if this is a PEFT model
        if os.path.exists(os.path.join(self.model_path, "adapter_config.json")):
            logger.info("Loading PEFT model...")
            # Load base model
            base_model = AutoModelForCausalLM.from_pretrained(
                self.base_model_name,
                torch_dtype=torch.float16,
                device_map="auto",
                trust_remote_code=True
            )
            # Load PEFT model
            self.model = PeftModel.from_pretrained(base_model, self.model_path)
        else:
            logger.info("Loading full model...")
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_path,
                torch_dtype=torch.float16,
                device_map="auto",
                trust_remote_code=True
            )
        
        self.model.eval()
        logger.info("Model loaded successfully")
    
    def generate_response(self, question: str, max_length: int = 2048, temperature: float = 0.7) -> str:
        """Generate response for a given question"""
        # Format the input
        messages = [
            {
                "role": "system",
                "content": "You are a helpful assistant that solves mathematical problems step by step."
            },
            {
                "role": "user",
                "content": f"Solve the following mathematical problem step by step. Provide a detailed solution and then give the final answer.\n\nProblem: {question}"
            }
        ]
        
        # Apply chat template
        text = self.tokenizer.apply_chat_template(
            messages,
            tokenize=False,
            add_generation_prompt=True
        )
        
        # Tokenize
        inputs = self.tokenizer(text, return_tensors="pt").to(self.device)
        
        # Generate
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=max_length,
                temperature=temperature,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=self.tokenizer.eos_token_id,
            )
        
        # Decode response
        response = self.tokenizer.decode(outputs[0][inputs.input_ids.shape[1]:], skip_special_tokens=True)
        return response.strip()
    
    def extract_final_answer(self, response: str) -> str:
        """Extract the final answer from the response"""
        # Look for patterns like "Final Answer: X" or "Answer: X"
        patterns = [
            r"Final Answer:\s*(.+?)(?:\n|$)",
            r"Answer:\s*(.+?)(?:\n|$)",
            r"Therefore,?\s*(.+?)(?:\n|$)",
            r"So,?\s*(.+?)(?:\n|$)"
        ]
        
        for pattern in patterns:
            match = re.search(pattern, response, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        
        # If no pattern found, return the last line
        lines = response.strip().split('\n')
        return lines[-1].strip() if lines else ""

class ModelEvaluator:
    """Evaluates the fine-tuned model"""
    
    def __init__(self, model_inference: ModelInference):
        self.model_inference = model_inference
        
    def evaluate_on_dataset(self, dataset_name: str = "GAIR/LIMO", num_samples: int = 100) -> Dict[str, Any]:
        """Evaluate model on a subset of the dataset"""
        logger.info(f"Evaluating on {num_samples} samples from {dataset_name}")
        
        # Load dataset
        dataset = load_dataset(dataset_name, split="train")
        
        # Select samples (different from training set)
        eval_samples = dataset.shuffle(seed=123).select(range(num_samples))
        
        results = []
        correct = 0
        
        for i, sample in enumerate(eval_samples):
            logger.info(f"Evaluating sample {i+1}/{num_samples}")
            
            question = sample['question']
            true_answer = sample['answer']
            
            # Generate response
            response = self.model_inference.generate_response(question)
            predicted_answer = self.model_inference.extract_final_answer(response)
            
            # Check if correct (simple string matching)
            is_correct = self.compare_answers(predicted_answer, true_answer)
            if is_correct:
                correct += 1
            
            result = {
                'question': question,
                'true_answer': true_answer,
                'predicted_answer': predicted_answer,
                'response': response,
                'correct': is_correct
            }
            results.append(result)
        
        accuracy = correct / num_samples
        
        evaluation_results = {
            'accuracy': accuracy,
            'correct': correct,
            'total': num_samples,
            'results': results
        }
        
        logger.info(f"Evaluation completed. Accuracy: {accuracy:.2%} ({correct}/{num_samples})")
        
        return evaluation_results
    
    def compare_answers(self, predicted: str, true: str) -> bool:
        """Compare predicted and true answers"""
        # Simple comparison - can be made more sophisticated
        predicted_clean = predicted.lower().strip()
        true_clean = true.lower().strip()
        
        # Direct match
        if predicted_clean == true_clean:
            return True
        
        # Check if predicted contains the true answer
        if true_clean in predicted_clean:
            return True
        
        # Extract numbers and compare
        pred_numbers = re.findall(r'-?\d+\.?\d*', predicted_clean)
        true_numbers = re.findall(r'-?\d+\.?\d*', true_clean)
        
        if pred_numbers and true_numbers:
            try:
                pred_num = float(pred_numbers[-1])  # Take the last number
                true_num = float(true_numbers[-1])
                return abs(pred_num - true_num) < 1e-6
            except ValueError:
                pass
        
        return False

def main():
    """Main evaluation function"""
    parser = argparse.ArgumentParser(description="Evaluate fine-tuned Qwen2.5-7B-Instruct model")
    parser.add_argument("--model_path", type=str, required=True, help="Path to the fine-tuned model")
    parser.add_argument("--base_model", type=str, default="Qwen/Qwen2.5-7B-Instruct", help="Base model name")
    parser.add_argument("--num_samples", type=int, default=100, help="Number of samples to evaluate")
    parser.add_argument("--output_file", type=str, help="Output file for results")
    parser.add_argument("--interactive", action="store_true", help="Interactive mode for testing")
    
    args = parser.parse_args()
    
    # Initialize model inference
    model_inference = ModelInference(args.model_path, args.base_model)
    model_inference.load_model()
    
    if args.interactive:
        # Interactive mode
        logger.info("Starting interactive mode. Type 'quit' to exit.")
        while True:
            question = input("\nEnter a mathematical problem: ")
            if question.lower() in ['quit', 'exit', 'q']:
                break
            
            print("\nGenerating response...")
            response = model_inference.generate_response(question)
            print(f"\nResponse:\n{response}")
            
            final_answer = model_inference.extract_final_answer(response)
            print(f"\nExtracted Answer: {final_answer}")
    
    else:
        # Evaluation mode
        evaluator = ModelEvaluator(model_inference)
        results = evaluator.evaluate_on_dataset(num_samples=args.num_samples)
        
        # Print summary
        print(f"\nEvaluation Results:")
        print(f"Accuracy: {results['accuracy']:.2%}")
        print(f"Correct: {results['correct']}/{results['total']}")
        
        # Save results if output file specified
        if args.output_file:
            with open(args.output_file, 'w') as f:
                json.dump(results, f, indent=2)
            logger.info(f"Results saved to {args.output_file}")
        
        # Print some examples
        print(f"\nSample Results:")
        for i, result in enumerate(results['results'][:5]):
            print(f"\n--- Example {i+1} ---")
            print(f"Question: {result['question'][:100]}...")
            print(f"True Answer: {result['true_answer']}")
            print(f"Predicted: {result['predicted_answer']}")
            print(f"Correct: {result['correct']}")

if __name__ == "__main__":
    main()
