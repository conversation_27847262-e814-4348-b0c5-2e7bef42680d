"""
Example usage script for KULLM-Pro fine-tuning pipeline
This script demonstrates how to use the fine-tuning pipeline with different configurations
"""

import os
import logging
from config import Config
from data_preprocessing import LIMODataProcessor
from finetune import LIMOTrainer

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def example_basic_training():
    """Example: Basic training with default settings"""
    logger.info("=== Example 1: Basic Training ===")
    
    # Use default configuration
    config = Config()
    
    # Initialize trainer
    trainer = LIMOTrainer(config)
    
    # Start training
    trainer.train()
    
    logger.info("Basic training completed!")

def example_custom_config():
    """Example: Training with custom configuration"""
    logger.info("=== Example 2: Custom Configuration ===")
    
    # Create custom configuration
    config = Config()
    
    # Modify settings for faster training (fewer samples, epochs)
    config.data.num_samples = 500  # Use fewer samples for quick test
    config.training.num_train_epochs = 2
    config.training.per_device_train_batch_size = 1  # Smaller batch for limited memory
    config.training.gradient_accumulation_steps = 16  # Compensate with more accumulation
    config.training.output_dir = "./qwen2.5-7b-limo-custom"
    config.training.run_name = "qwen2.5-limo-custom-test"
    
    # Enable memory optimizations
    config.model.load_in_4bit = True
    config.training.gradient_checkpointing = True
    
    # Adjust LoRA settings
    config.lora.r = 8  # Smaller rank for faster training
    config.lora.lora_alpha = 16
    
    logger.info("Custom configuration:")
    logger.info(f"  Samples: {config.data.num_samples}")
    logger.info(f"  Epochs: {config.training.num_train_epochs}")
    logger.info(f"  Batch size: {config.training.per_device_train_batch_size}")
    logger.info(f"  LoRA rank: {config.lora.r}")
    logger.info(f"  4-bit quantization: {config.model.load_in_4bit}")
    
    # Initialize trainer
    trainer = LIMOTrainer(config)
    
    # Start training
    trainer.train()
    
    logger.info("Custom training completed!")

def example_data_preprocessing_only():
    """Example: Data preprocessing only"""
    logger.info("=== Example 3: Data Preprocessing Only ===")
    
    config = Config()
    config.data.num_samples = 100  # Small sample for testing
    
    # Initialize data processor
    processor = LIMODataProcessor(config)
    
    # Process data
    train_dataset, val_dataset = processor.process_data()
    
    # Print sample
    sample = train_dataset[0]
    logger.info("Sample processed data:")
    logger.info(f"  Question: {sample['question'][:100]}...")
    logger.info(f"  Answer: {sample['answer']}")
    logger.info(f"  Messages: {len(sample['messages'])} messages")
    
    logger.info("Data preprocessing completed!")

def example_memory_efficient_training():
    """Example: Memory-efficient training for limited GPU memory"""
    logger.info("=== Example 4: Memory-Efficient Training ===")
    
    config = Config()
    
    # Memory optimization settings
    config.model.load_in_4bit = True  # 4-bit quantization
    config.training.per_device_train_batch_size = 1  # Minimal batch size
    config.training.gradient_accumulation_steps = 32  # Large accumulation
    config.training.gradient_checkpointing = True  # Enable checkpointing
    config.training.dataloader_num_workers = 0  # Reduce memory usage
    config.training.fp16 = True  # Use fp16 instead of bf16
    config.training.bf16 = False
    
    # Reduce model complexity
    config.lora.r = 4  # Very small LoRA rank
    config.lora.lora_alpha = 8
    
    # Shorter sequences
    config.model.max_length = 1024
    
    # Fewer samples for testing
    config.data.num_samples = 200
    config.training.num_train_epochs = 1
    
    config.training.output_dir = "./qwen2.5-7b-limo-memory-efficient"
    config.training.run_name = "qwen2.5-limo-memory-efficient"
    
    logger.info("Memory-efficient configuration:")
    logger.info(f"  4-bit quantization: {config.model.load_in_4bit}")
    logger.info(f"  Batch size: {config.training.per_device_train_batch_size}")
    logger.info(f"  Gradient accumulation: {config.training.gradient_accumulation_steps}")
    logger.info(f"  LoRA rank: {config.lora.r}")
    logger.info(f"  Max length: {config.model.max_length}")
    
    # Initialize trainer
    trainer = LIMOTrainer(config)
    
    # Start training
    trainer.train()
    
    logger.info("Memory-efficient training completed!")

def main():
    """Main function to run examples"""
    print("KULLM-Pro Fine-tuning Examples")
    print("=" * 50)
    print("1. Basic training with default settings")
    print("2. Custom configuration training")
    print("3. Data preprocessing only")
    print("4. Memory-efficient training")
    print("5. Exit")
    
    while True:
        try:
            choice = input("\nSelect an example (1-5): ").strip()
            
            if choice == "1":
                example_basic_training()
                break
            elif choice == "2":
                example_custom_config()
                break
            elif choice == "3":
                example_data_preprocessing_only()
                break
            elif choice == "4":
                example_memory_efficient_training()
                break
            elif choice == "5":
                print("Exiting...")
                break
            else:
                print("Invalid choice. Please select 1-5.")
                
        except KeyboardInterrupt:
            print("\nExiting...")
            break
        except Exception as e:
            logger.error(f"Error running example: {e}")
            break

if __name__ == "__main__":
    main()
