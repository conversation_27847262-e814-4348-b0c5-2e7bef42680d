"""
Main fine-tuning script for Qwen2.5-7B-Instruct on GAIR/LIMO dataset
"""

import os
import logging
import torch
import wandb
from transformers import Trainer
from datasets import load_from_disk
import argparse

from config import Config
from data_preprocessing import LIMODataProcessor
from training_utils import ModelSetup, DatasetProcessor, setup_training_arguments, setup_data_collator

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LIMOTrainer:
    """Main trainer class for LIMO fine-tuning"""
    
    def __init__(self, config: Config):
        self.config = config
        self.model_setup = ModelSetup(config)
        self.tokenizer = None
        self.model = None
        self.train_dataset = None
        self.val_dataset = None
        
    def prepare_data(self):
        """Prepare training data"""
        logger.info("Preparing training data...")
        
        # Check if processed data exists
        processed_data_dir = os.path.join(self.config.cache_dir, "processed_data")
        train_path = os.path.join(processed_data_dir, "train_dataset")
        val_path = os.path.join(processed_data_dir, "val_dataset")
        
        if os.path.exists(train_path) and os.path.exists(val_path):
            logger.info("Loading existing processed data...")
            train_dataset = load_from_disk(train_path)
            val_dataset = load_from_disk(val_path)
        else:
            logger.info("Processing data from scratch...")
            processor = LIMODataProcessor(self.config)
            train_dataset, val_dataset = processor.process_data()
        
        return train_dataset, val_dataset
    
    def setup_model_and_tokenizer(self):
        """Setup model and tokenizer"""
        logger.info("Setting up model and tokenizer...")
        
        # Setup tokenizer
        self.tokenizer = self.model_setup.setup_tokenizer()
        
        # Setup model
        self.model = self.model_setup.setup_model()
        
        # Setup LoRA
        self.model = self.model_setup.setup_lora(self.model)
        
        return self.model, self.tokenizer
    
    def prepare_datasets_for_training(self, train_dataset, val_dataset):
        """Prepare datasets for training"""
        logger.info("Preparing datasets for training...")
        
        dataset_processor = DatasetProcessor(self.config, self.tokenizer)
        train_dataset, val_dataset = dataset_processor.process_datasets(
            train_dataset, val_dataset
        )
        
        return train_dataset, val_dataset
    
    def train(self):
        """Main training function"""
        logger.info("Starting fine-tuning process...")
        
        # Initialize wandb if configured
        if self.config.training.report_to == "wandb":
            wandb.init(
                project="qwen2.5-limo-finetune",
                name=self.config.training.run_name,
                config=self.config.__dict__
            )
        
        try:
            # Prepare data
            train_dataset, val_dataset = self.prepare_data()
            
            # Setup model and tokenizer
            self.model, self.tokenizer = self.setup_model_and_tokenizer()
            
            # Prepare datasets for training
            train_dataset, val_dataset = self.prepare_datasets_for_training(
                train_dataset, val_dataset
            )
            
            # Setup training arguments
            training_args = setup_training_arguments(self.config)
            
            # Setup data collator
            data_collator = setup_data_collator(self.tokenizer)
            
            # Initialize trainer
            trainer = Trainer(
                model=self.model,
                args=training_args,
                train_dataset=train_dataset,
                eval_dataset=val_dataset,
                tokenizer=self.tokenizer,
                data_collator=data_collator,
            )
            
            # Start training
            logger.info("Starting training...")
            trainer.train()
            
            # Save the final model
            logger.info("Saving final model...")
            trainer.save_model()
            self.tokenizer.save_pretrained(self.config.training.output_dir)
            
            # Save training state
            trainer.save_state()
            
            logger.info("Training completed successfully!")
            
            # Log final metrics
            if hasattr(trainer.state, 'log_history') and trainer.state.log_history:
                final_metrics = trainer.state.log_history[-1]
                logger.info(f"Final metrics: {final_metrics}")
            
        except Exception as e:
            logger.error(f"Training failed with error: {e}")
            raise
        
        finally:
            # Cleanup
            if self.config.training.report_to == "wandb":
                wandb.finish()
    
    def evaluate(self):
        """Evaluate the trained model"""
        logger.info("Evaluating model...")
        
        # This can be extended to include custom evaluation metrics
        # For now, we rely on the evaluation during training
        pass

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Fine-tune Qwen2.5-7B-Instruct on GAIR/LIMO dataset")
    parser.add_argument("--config", type=str, help="Path to config file (optional)")
    parser.add_argument("--num_samples", type=int, default=1000, help="Number of samples to use")
    parser.add_argument("--epochs", type=int, default=3, help="Number of training epochs")
    parser.add_argument("--batch_size", type=int, default=2, help="Training batch size")
    parser.add_argument("--learning_rate", type=float, default=2e-4, help="Learning rate")
    parser.add_argument("--output_dir", type=str, help="Output directory")
    parser.add_argument("--no_wandb", action="store_true", help="Disable wandb logging")
    
    args = parser.parse_args()
    
    # Create config
    config = Config()
    
    # Override config with command line arguments
    if args.num_samples:
        config.data.num_samples = args.num_samples
    if args.epochs:
        config.training.num_train_epochs = args.epochs
    if args.batch_size:
        config.training.per_device_train_batch_size = args.batch_size
    if args.learning_rate:
        config.training.learning_rate = args.learning_rate
    if args.output_dir:
        config.training.output_dir = args.output_dir
    if args.no_wandb:
        config.training.report_to = "none"
    
    # Print configuration
    logger.info("Training Configuration:")
    logger.info(f"  Model: {config.model.model_name}")
    logger.info(f"  Dataset: {config.data.dataset_name}")
    logger.info(f"  Number of samples: {config.data.num_samples}")
    logger.info(f"  Epochs: {config.training.num_train_epochs}")
    logger.info(f"  Batch size: {config.training.per_device_train_batch_size}")
    logger.info(f"  Learning rate: {config.training.learning_rate}")
    logger.info(f"  Output directory: {config.training.output_dir}")
    logger.info(f"  LoRA r: {config.lora.r}")
    logger.info(f"  LoRA alpha: {config.lora.lora_alpha}")
    
    # Check GPU availability
    if torch.cuda.is_available():
        logger.info(f"CUDA available: {torch.cuda.device_count()} GPU(s)")
        logger.info(f"Current device: {torch.cuda.current_device()}")
        logger.info(f"Device name: {torch.cuda.get_device_name()}")
    else:
        logger.warning("CUDA not available. Training will be slow on CPU.")
    
    # Initialize trainer and start training
    trainer = LIMOTrainer(config)
    trainer.train()

if __name__ == "__main__":
    main()
