#!/usr/bin/env python3
"""
Script to fix dependency compatibility issues
"""

import subprocess
import sys
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_command(command, description=""):
    """Run a shell command and handle errors"""
    logger.info(f"Running: {description or command}")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            text=True,
            capture_output=True
        )
        
        logger.info(f"✅ {description} completed successfully")
        if result.stdout:
            logger.info(f"Output: {result.stdout.strip()}")
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ {description} failed with return code {e.returncode}")
        if e.stderr:
            logger.error(f"Error: {e.stderr.strip()}")
        return False

def main():
    """Fix dependency compatibility issues"""
    
    logger.info("=" * 60)
    logger.info("FIXING DEPENDENCY COMPATIBILITY ISSUES")
    logger.info("=" * 60)
    
    # Commands to fix dependencies
    commands = [
        ("pip install --upgrade accelerate>=0.34.0", "Upgrade accelerate"),
        ("pip install transformers>=4.44.0,<4.46.0", "Install compatible transformers"),
        ("pip install torch>=2.0.0", "Ensure torch compatibility"),
        ("pip install peft>=0.7.0", "Ensure PEFT compatibility"),
    ]
    
    success_count = 0
    
    for command, description in commands:
        if run_command(command, description):
            success_count += 1
        else:
            logger.error(f"Failed to execute: {description}")
    
    logger.info("=" * 60)
    logger.info("DEPENDENCY FIX SUMMARY")
    logger.info("=" * 60)
    
    if success_count == len(commands):
        logger.info("🎉 All dependency fixes completed successfully!")
        logger.info("")
        logger.info("You can now run training:")
        logger.info("python train.py --train_file ./data/limo_original_1000_thinking.jsonl --output_dir ./models/baseline_thinking --epochs 3 --batch_size 2")
    else:
        logger.error(f"❌ {len(commands) - success_count} dependency fixes failed")
        logger.info("")
        logger.info("Try running manually:")
        logger.info("pip install --upgrade accelerate>=0.34.0")
        logger.info("pip install 'transformers>=4.44.0,<4.46.0'")
        sys.exit(1)

if __name__ == "__main__":
    main()
