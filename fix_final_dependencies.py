#!/usr/bin/env python3
"""
Final fix for all dependency compatibility issues
"""

import subprocess
import sys
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_command(command, description=""):
    """Run a shell command and handle errors"""
    logger.info(f"Running: {description or command}")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            text=True,
            capture_output=True
        )
        
        logger.info(f"✅ {description} completed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ {description} failed with return code {e.returncode}")
        if e.stderr:
            logger.error(f"Error: {e.stderr.strip()}")
        return False

def main():
    """Fix all dependency compatibility issues with older, stable versions"""
    
    logger.info("=" * 60)
    logger.info("FINAL DEPENDENCY COMPATIBILITY FIX")
    logger.info("=" * 60)
    
    # Uninstall all problematic packages
    uninstall_commands = [
        ("pip uninstall -y transformers accelerate peft", "Uninstall all conflicting packages"),
    ]
    
    # Install older, stable versions that work together
    install_commands = [
        ("pip install torch>=2.0.0", "Install PyTorch"),
        ("pip install accelerate==0.25.0", "Install older stable accelerate"),
        ("pip install transformers==4.36.2", "Install older stable transformers"),
        ("pip install peft==0.7.1", "Install older stable peft"),
        ("pip install datasets>=2.14.0", "Install datasets"),
        ("pip install bitsandbytes>=0.41.0", "Install bitsandbytes"),
    ]
    
    all_commands = uninstall_commands + install_commands
    success_count = 0
    
    for command, description in all_commands:
        if run_command(command, description):
            success_count += 1
        else:
            logger.error(f"Failed to execute: {description}")
    
    logger.info("=" * 60)
    logger.info("FINAL DEPENDENCY FIX SUMMARY")
    logger.info("=" * 60)
    
    if success_count == len(all_commands):
        logger.info("🎉 All dependency fixes completed successfully!")
        logger.info("")
        logger.info("Stable versions installed:")
        logger.info("  - accelerate==0.25.0")
        logger.info("  - transformers==4.36.2") 
        logger.info("  - peft==0.7.1")
        logger.info("")
        logger.info("These are older but stable versions that work together.")
        logger.info("You can now run training:")
        logger.info("python train_simple.py --train_file ./data/limo_original_1000_thinking.jsonl --output_dir ./models/baseline_thinking --epochs 1 --batch_size 1")
    else:
        logger.error(f"❌ {len(all_commands) - success_count} dependency fixes failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
