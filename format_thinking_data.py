"""
<PERSON><PERSON><PERSON> to reformat JSONL data for thinking model training
Separates solution process (thinking) from final answer (response)
"""

import json
import re
import logging
from typing import Dict, Any, List

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def extract_final_answer(content: str) -> tuple[str, str]:
    """
    Extract the final answer from the content and separate thinking from response
    Returns: (thinking_content, final_answer)
    """
    # Look for patterns like "Final Answer: X" or "**Final Answer**\n\\boxed{X}"
    final_answer_patterns = [
        r'Final Answer:\s*(.+?)(?:\n|$)',
        r'\*\*Final Answer\*\*\s*\\boxed\{([^}]+)\}',
        r'\\boxed\{([^}]+)\}',
        r'Therefore,?\s*(?:the answer is|the result is|we get)\s*(.+?)(?:\.|$)',
    ]
    
    final_answer = None
    thinking_content = content
    
    # Try to find final answer using patterns
    for pattern in final_answer_patterns:
        matches = list(re.finditer(pattern, content, re.IGNORECASE | re.MULTILINE))
        if matches:
            # Take the last match (most likely to be the final answer)
            match = matches[-1]
            final_answer = match.group(1).strip()
            
            # Remove the final answer section from thinking content
            # Keep everything before the final answer
            thinking_content = content[:match.start()].strip()
            break
    
    # If no pattern found, try to split on common final answer indicators
    if final_answer is None:
        split_indicators = [
            "**Final Answer**",
            "Final Answer:",
            "Therefore, the answer is",
            "So the answer is",
            "The answer is"
        ]
        
        for indicator in split_indicators:
            if indicator in content:
                parts = content.split(indicator, 1)
                if len(parts) == 2:
                    thinking_content = parts[0].strip()
                    final_answer = parts[1].strip()
                    # Clean up the final answer
                    final_answer = re.sub(r'\\boxed\{([^}]+)\}', r'\1', final_answer)
                    final_answer = final_answer.replace('\n', ' ').strip()
                    break
    
    # If still no final answer found, try to extract the last number or expression
    if final_answer is None:
        # Look for the last number or mathematical expression
        number_patterns = [
            r'(\d+(?:\.\d+)?)\s*(?:\.|$)',
            r'(\d+/\d+)\s*(?:\.|$)',
            r'([a-zA-Z]\s*=\s*\d+)',
        ]
        
        for pattern in number_patterns:
            matches = list(re.finditer(pattern, content))
            if matches:
                final_answer = matches[-1].group(1).strip()
                break
    
    # Default fallback
    if final_answer is None:
        # Split content roughly in half, assuming second half contains the answer
        lines = content.split('\n')
        mid_point = len(lines) * 3 // 4  # Take last quarter as potential answer
        thinking_content = '\n'.join(lines[:mid_point]).strip()
        final_answer = '\n'.join(lines[mid_point:]).strip()
    
    return thinking_content, final_answer

def format_for_thinking_model(item: Dict[str, Any]) -> Dict[str, Any]:
    """
    Reformat a single JSONL item for thinking model training
    """
    messages = item.get('messages', [])
    if len(messages) < 3:  # Should have system, user, assistant
        return item
    
    system_msg = messages[0]
    user_msg = messages[1] 
    assistant_msg = messages[2]
    
    if assistant_msg['role'] != 'assistant':
        return item
    
    # Extract thinking and final answer from assistant content
    full_content = assistant_msg['content']
    thinking_content, final_answer = extract_final_answer(full_content)
    
    # Create new format with thinking tokens
    new_assistant_content = f"<think>\n{thinking_content}\n</think>\n\n{final_answer}"
    
    # Create new item with updated assistant message
    new_item = {
        'messages': [
            system_msg,
            user_msg,
            {
                'role': 'assistant',
                'content': new_assistant_content
            }
        ]
    }
    
    return new_item

def process_jsonl_file(input_file: str, output_file: str):
    """
    Process a JSONL file and reformat for thinking model training
    """
    logger.info(f"Processing {input_file} -> {output_file}")
    
    processed_count = 0
    error_count = 0
    
    with open(input_file, 'r', encoding='utf-8') as infile, \
         open(output_file, 'w', encoding='utf-8') as outfile:
        
        for line_num, line in enumerate(infile, 1):
            line = line.strip()
            if not line:
                continue
                
            try:
                item = json.loads(line)
                formatted_item = format_for_thinking_model(item)
                outfile.write(json.dumps(formatted_item, ensure_ascii=False) + '\n')
                processed_count += 1
                
                if processed_count % 100 == 0:
                    logger.info(f"Processed {processed_count} items...")
                    
            except Exception as e:
                logger.error(f"Error processing line {line_num}: {e}")
                error_count += 1
                continue
    
    logger.info(f"Processing complete!")
    logger.info(f"Successfully processed: {processed_count} items")
    logger.info(f"Errors: {error_count} items")

def main():
    """
    Main function to process both baseline and code-switched datasets
    """
    import argparse
    
    parser = argparse.ArgumentParser(description="Reformat JSONL data for thinking model training")
    parser.add_argument("--input_file", type=str, required=True, help="Input JSONL file")
    parser.add_argument("--output_file", type=str, required=True, help="Output JSONL file")
    
    args = parser.parse_args()
    
    process_jsonl_file(args.input_file, args.output_file)

if __name__ == "__main__":
    main()
