"""
JSONL data processor for training pipeline
"""

import json
import logging
import os
from typing import List, Dict, Any, Tuple, Optional
from sklearn.model_selection import train_test_split
from datasets import Dataset
import random

from config import Config

logger = logging.getLogger(__name__)

class JSONLDataProcessor:
    """Data processor for JSONL files"""
    
    def __init__(self, config: Config):
        self.config = config
        
    def load_jsonl(self, file_path: str) -> List[Dict[str, Any]]:
        """Load data from JSONL file"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"JSONL file not found: {file_path}")
        
        data = []
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    item = json.loads(line.strip())
                    data.append(item)
                except json.JSONDecodeError as e:
                    logger.warning(f"Skipping invalid JSON on line {line_num}: {e}")
                    continue
        
        logger.info(f"Loaded {len(data)} items from {file_path}")
        return data
    
    def save_jsonl(self, data: List[Dict[str, Any]], file_path: str):
        """Save data to JSONL file"""
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            for item in data:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
        
        logger.info(f"Saved {len(data)} items to {file_path}")
    
    def validate_messages_format(self, item: Dict[str, Any]) -> bool:
        """Validate that an item has the correct messages format"""
        if "messages" not in item:
            return False
        
        messages = item["messages"]
        if not isinstance(messages, list) or len(messages) < 2:
            return False
        
        # Check that messages have role and content
        for msg in messages:
            if not isinstance(msg, dict) or "role" not in msg or "content" not in msg:
                return False
        
        return True
    
    def split_data(self, data: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """Split data into train and validation sets"""
        if self.config.data.test_size <= 0:
            return data, []
        
        train_data, val_data = train_test_split(
            data,
            test_size=self.config.data.test_size,
            random_state=self.config.data.random_seed,
            shuffle=True
        )
        
        logger.info(f"Split data: {len(train_data)} train, {len(val_data)} validation")
        return train_data, val_data
    
    def load_train_val_data(self) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """Load training and validation data"""
        
        # Load training data
        train_data = self.load_jsonl(self.config.data.train_file)
        
        # Validate format
        valid_train_data = []
        for i, item in enumerate(train_data):
            if self.validate_messages_format(item):
                valid_train_data.append(item)
            else:
                logger.warning(f"Skipping invalid item at index {i}: missing or invalid messages format")
        
        logger.info(f"Valid training items: {len(valid_train_data)}/{len(train_data)}")
        
        # Load or split validation data
        if self.config.data.val_file and os.path.exists(self.config.data.val_file):
            logger.info("Loading separate validation file")
            val_data = self.load_jsonl(self.config.data.val_file)
            
            # Validate validation data
            valid_val_data = []
            for i, item in enumerate(val_data):
                if self.validate_messages_format(item):
                    valid_val_data.append(item)
                else:
                    logger.warning(f"Skipping invalid validation item at index {i}")
            
            logger.info(f"Valid validation items: {len(valid_val_data)}/{len(val_data)}")
            
        else:
            logger.info("Splitting training data for validation")
            valid_train_data, valid_val_data = self.split_data(valid_train_data)
        
        return valid_train_data, valid_val_data
    
    def convert_to_hf_dataset(self, data: List[Dict[str, Any]]) -> Dataset:
        """Convert list of dictionaries to HuggingFace Dataset"""
        return Dataset.from_list(data)
    
    def process_datasets(self) -> Tuple[Dataset, Dataset]:
        """Main processing function that returns HuggingFace datasets"""
        logger.info("Processing JSONL datasets...")
        
        # Load data
        train_data, val_data = self.load_train_val_data()
        
        # Convert to HuggingFace datasets
        train_dataset = self.convert_to_hf_dataset(train_data)
        val_dataset = self.convert_to_hf_dataset(val_data)
        
        logger.info(f"Created datasets: {len(train_dataset)} train, {len(val_dataset)} validation")
        
        return train_dataset, val_dataset
    
    def create_sample_data(self, output_file: str, num_samples: int = 10):
        """Create a small sample dataset for testing"""
        logger.info(f"Creating sample dataset with {num_samples} items")
        
        sample_data = []
        for i in range(num_samples):
            item = {
                "messages": [
                    {
                        "role": "system",
                        "content": "You are a helpful assistant that solves mathematical problems step by step."
                    },
                    {
                        "role": "user",
                        "content": f"Solve this sample problem {i+1}: What is 2 + 2?"
                    },
                    {
                        "role": "assistant",
                        "content": f"To solve 2 + 2:\n\nStep 1: We have two numbers to add: 2 and 2.\nStep 2: Adding them together: 2 + 2 = 4\n\nFinal Answer: 4"
                    }
                ]
            }
            sample_data.append(item)
        
        self.save_jsonl(sample_data, output_file)
        logger.info(f"Sample dataset saved to {output_file}")

class DatasetTokenizer:
    """Tokenizer for JSONL datasets"""
    
    def __init__(self, tokenizer, max_length: int = 2048):
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def tokenize_messages(self, examples):
        """Tokenize messages using chat template"""
        texts = []
        
        for messages in examples["messages"]:
            # Apply chat template
            text = self.tokenizer.apply_chat_template(
                messages,
                tokenize=False,
                add_generation_prompt=False
            )
            texts.append(text)
        
        # Tokenize
        tokenized = self.tokenizer(
            texts,
            truncation=True,
            padding=False,
            max_length=self.max_length,
            return_tensors=None
        )
        
        # For causal language modeling, labels are the same as input_ids
        tokenized["labels"] = tokenized["input_ids"].copy()
        
        return tokenized
    
    def process_dataset(self, dataset: Dataset) -> Dataset:
        """Process dataset with tokenization"""
        logger.info("Tokenizing dataset...")
        
        # Remove any columns that might interfere with tokenization
        columns_to_remove = [col for col in dataset.column_names if col not in ["messages"]]
        
        tokenized_dataset = dataset.map(
            self.tokenize_messages,
            batched=True,
            remove_columns=columns_to_remove,
            desc="Tokenizing dataset"
        )
        
        logger.info(f"Tokenized dataset: {len(tokenized_dataset)} samples")
        return tokenized_dataset

def main():
    """Test function"""
    from config import Config
    
    config = Config()
    
    # Create sample data if it doesn't exist
    processor = JSONLDataProcessor(config)
    
    if not os.path.exists(config.data.train_file):
        os.makedirs(os.path.dirname(config.data.train_file), exist_ok=True)
        processor.create_sample_data(config.data.train_file, num_samples=20)
    
    # Test loading and processing
    try:
        train_dataset, val_dataset = processor.process_datasets()
        
        print(f"Train dataset: {len(train_dataset)} samples")
        print(f"Val dataset: {len(val_dataset)} samples")
        
        # Show sample
        if len(train_dataset) > 0:
            sample = train_dataset[0]
            print(f"\nSample messages: {len(sample['messages'])}")
            for i, msg in enumerate(sample['messages']):
                print(f"  Message {i+1} ({msg['role']}): {msg['content'][:100]}...")
        
    except Exception as e:
        logger.error(f"Error processing datasets: {e}")
        raise

if __name__ == "__main__":
    main()
