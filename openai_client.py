"""
OpenAI API client for code-switching data processing
"""

import os
import asyncio
import logging
import time
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import json

import openai
from openai import AsyncOpenAI
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
import aiohttp
from asyncio_throttle import Throttler

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class OpenAIConfig:
    """Configuration for OpenAI API"""
    api_key: Optional[str] = None
    model: str = "o1-mini"
    max_tokens: int = 32000
    temperature: float = 1.0
    max_concurrent_requests: int = 5
    requests_per_minute: int = 50
    max_retries: int = 3
    retry_delay: float = 1.0
    timeout: int = 300  # 5 minutes timeout for o1-mini

    def __post_init__(self):
        if self.api_key is None:
            self.api_key = os.getenv("OPENAI_API_KEY")
        if not self.api_key:
            raise ValueError("OpenAI API key not found. Set OPENAI_API_KEY environment variable.")

class OpenAIClient:
    """Async OpenAI client with rate limiting and error handling"""
    
    def __init__(self, config: OpenAIConfig):
        self.config = config
        self.client = AsyncOpenAI(api_key=config.api_key)
        
        # Set up rate limiting
        # o1-mini has specific rate limits, so we need to be conservative
        self.throttler = Throttler(rate_limit=config.requests_per_minute, period=60)
        self.semaphore = asyncio.Semaphore(config.max_concurrent_requests)
        
        # Track usage
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.total_tokens_used = 0
        
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((openai.RateLimitError, openai.APITimeoutError, aiohttp.ClientError))
    )
    async def _make_request(self, messages: List[Dict[str, str]]) -> Dict[str, Any]:
        """Make a single API request with retry logic"""
        
        async with self.semaphore:
            async with self.throttler:
                try:
                    self.total_requests += 1
                    
                    # For o1-mini, we don't use system messages in the same way
                    # We need to incorporate the system prompt into the user message
                    response = await self.client.chat.completions.create(
                        model=self.config.model,
                        messages=messages,
                        max_completion_tokens=self.config.max_tokens,
                        temperature=self.config.temperature,
                        timeout=self.config.timeout
                    )
                    
                    self.successful_requests += 1
                    
                    # Track token usage
                    if hasattr(response, 'usage') and response.usage:
                        self.total_tokens_used += response.usage.total_tokens
                    
                    return {
                        "success": True,
                        "response": response.choices[0].message.content,
                        "usage": response.usage.model_dump() if response.usage else None,
                        "model": response.model
                    }
                    
                except openai.RateLimitError as e:
                    logger.warning(f"Rate limit hit: {e}")
                    self.failed_requests += 1
                    raise
                    
                except openai.APITimeoutError as e:
                    logger.warning(f"API timeout: {e}")
                    self.failed_requests += 1
                    raise
                    
                except Exception as e:
                    logger.error(f"API request failed: {e}")
                    self.failed_requests += 1
                    return {
                        "success": False,
                        "error": str(e),
                        "response": None
                    }
    
    async def process_single_item(self, system_prompt: str, user_content: str) -> Dict[str, Any]:
        """Process a single item through the API"""
        
        # For o1-mini, we combine system prompt with user content
        # since o1 models don't use system messages in the traditional way
        combined_prompt = f"{system_prompt}\n\nInput: {user_content}"
        
        messages = [
            {
                "role": "user",
                "content": combined_prompt
            }
        ]
        
        result = await self._make_request(messages)
        return result
    
    async def process_batch(self, system_prompt: str, items: List[str], 
                          progress_callback: Optional[callable] = None) -> List[Dict[str, Any]]:
        """Process a batch of items"""
        
        logger.info(f"Processing batch of {len(items)} items")
        
        # Create tasks for all items
        tasks = []
        for i, item in enumerate(items):
            task = self.process_single_item(system_prompt, item)
            tasks.append(task)
        
        # Process with progress tracking
        results = []
        for i, task in enumerate(asyncio.as_completed(tasks)):
            result = await task
            results.append(result)
            
            if progress_callback:
                progress_callback(i + 1, len(items))
            
            # Log progress every 10 items
            if (i + 1) % 10 == 0:
                logger.info(f"Processed {i + 1}/{len(items)} items")
        
        return results
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """Get usage statistics"""
        return {
            "total_requests": self.total_requests,
            "successful_requests": self.successful_requests,
            "failed_requests": self.failed_requests,
            "success_rate": self.successful_requests / max(self.total_requests, 1),
            "total_tokens_used": self.total_tokens_used,
            "estimated_cost": self.estimate_cost()
        }
    
    def estimate_cost(self) -> float:
        """Estimate cost based on token usage"""
        # o1-mini pricing (as of 2024): $3.00 per 1M input tokens, $12.00 per 1M output tokens
        # For simplicity, we'll estimate average cost
        estimated_cost_per_1k_tokens = 0.007  # Rough average
        return (self.total_tokens_used / 1000) * estimated_cost_per_1k_tokens

class CodeSwitchingProcessor:
    """Main processor for code-switching using OpenAI API"""
    
    def __init__(self, config: OpenAIConfig, system_prompt_path: str):
        self.config = config
        self.client = OpenAIClient(config)
        self.system_prompt = self._load_system_prompt(system_prompt_path)
        
    def _load_system_prompt(self, path: str) -> str:
        """Load system prompt from file"""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                prompt = f.read().strip()
            logger.info(f"Loaded system prompt from {path} ({len(prompt)} characters)")
            return prompt
        except Exception as e:
            logger.error(f"Failed to load system prompt from {path}: {e}")
            raise
    
    async def process_limo_solutions(self, limo_data: List[Dict[str, Any]], 
                                   save_progress_every: int = 50) -> List[Dict[str, Any]]:
        """Process LIMO solutions for code-switching"""
        
        logger.info(f"Starting code-switching processing for {len(limo_data)} items")
        
        results = []
        failed_items = []
        
        # Process in smaller batches to manage memory and provide progress updates
        batch_size = 20
        
        for i in range(0, len(limo_data), batch_size):
            batch = limo_data[i:i + batch_size]
            batch_solutions = [item['solution'] for item in batch]
            
            logger.info(f"Processing batch {i//batch_size + 1}/{(len(limo_data) + batch_size - 1)//batch_size}")
            
            # Process batch
            batch_results = await self.client.process_batch(
                self.system_prompt, 
                batch_solutions,
                progress_callback=lambda current, total: logger.info(f"Batch progress: {current}/{total}")
            )
            
            # Combine results with original data
            for j, (original_item, api_result) in enumerate(zip(batch, batch_results)):
                if api_result.get("success", False):
                    processed_item = {
                        "question": original_item["question"],
                        "original_solution": original_item["solution"],
                        "code_switched_solution": api_result["response"],
                        "answer": original_item["answer"],
                        "processing_info": {
                            "model": api_result.get("model", self.config.model),
                            "usage": api_result.get("usage"),
                            "timestamp": time.time()
                        }
                    }
                    results.append(processed_item)
                else:
                    failed_item = {
                        "index": i + j,
                        "original_item": original_item,
                        "error": api_result.get("error", "Unknown error")
                    }
                    failed_items.append(failed_item)
                    logger.error(f"Failed to process item {i + j}: {failed_item['error']}")
            
            # Save progress periodically
            if (i + batch_size) % (save_progress_every * batch_size) == 0:
                self._save_progress(results, failed_items, i + batch_size)
        
        # Final statistics
        stats = self.client.get_usage_stats()
        logger.info("Processing completed!")
        logger.info(f"Successfully processed: {len(results)}/{len(limo_data)}")
        logger.info(f"Failed: {len(failed_items)}")
        logger.info(f"API Stats: {stats}")
        
        return results, failed_items
    
    def _save_progress(self, results: List[Dict], failed_items: List[Dict], processed_count: int):
        """Save progress to temporary files"""
        timestamp = int(time.time())
        
        # Save successful results
        results_file = f"code_switched_progress_{timestamp}.jsonl"
        with open(results_file, 'w', encoding='utf-8') as f:
            for item in results:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
        
        # Save failed items
        if failed_items:
            failed_file = f"failed_items_{timestamp}.json"
            with open(failed_file, 'w', encoding='utf-8') as f:
                json.dump(failed_items, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Progress saved: {len(results)} successful, {len(failed_items)} failed (processed {processed_count} total)")
