"""
Quick start script for KULLM-Pro code-switching pipeline
"""

import os
import sys
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def check_requirements():
    """Check if all requirements are met"""
    logger.info("Checking requirements...")

    # Check OpenAI API key
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        logger.error("OpenAI API key not found!")
        logger.error("Please set your OpenAI API key:")
        logger.error("  export OPENAI_API_KEY='your-api-key-here'")
        return False

    logger.info("✓ OpenAI API key found")

    # Check system prompt file
    if not os.path.exists("system_prompt.txt"):
        logger.error("system_prompt.txt not found!")
        return False

    logger.info("✓ System prompt file found")

    # Check if required Python packages are available
    try:
        import openai
        import datasets
        import transformers

        logger.info("✓ Required packages available")
    except ImportError as e:
        logger.error(f"Missing required package: {e}")
        logger.error("Please install requirements: pip install -r requirements.txt")
        return False

    return True


def main():
    """Main quick start function"""
    print("=" * 60)
    print("KULLM-Pro Code-Switching Pipeline - Quick Start")
    print("=" * 60)

    if not check_requirements():
        print("\n❌ Requirements check failed. Please fix the issues above.")
        sys.exit(1)

    print("\n✅ All requirements met!")

    print("\n" + "=" * 60)
    print("QUICK START OPTIONS")
    print("=" * 60)

    print("\n1. Create datasets only (recommended first step):")
    print("   python create_code_switched_data.py --num_samples 100")

    print("\n2. Run complete pipeline (datasets + training):")
    print("   python run_pipeline.py --num_samples 100")

    print("\n3. Train on existing data:")
    print("   python finetune.py --train_file ./data/limo_original_1000.jsonl")

    print("\n4. Test with small sample:")
    print("   python run_pipeline.py --num_samples 10 --max_concurrent 1")

    print("\n" + "=" * 60)
    print("IMPORTANT NOTES")
    print("=" * 60)

    print("\n• ✅ AFFORDABLE: o4-mini with Batch API is cost-effective!")
    print("• 💰 Much cheaper: 1000 samples = $15-50 (vs $1,500-5,000)")
    print("• ⚡ Fast batch processing: 1000 samples in 1-3 hours")
    print("• 📊 Monitor your OpenAI API usage (but costs are reasonable)")
    print("• 🎯 The pipeline creates two models: baseline and code-switched")
    print("• � Batch API provides 50% cost savings automatically")

    print("\n" + "=" * 60)
    print("FILE STRUCTURE")
    print("=" * 60)

    print("\nAfter running, you'll have:")
    print("  ./data/")
    print("    ├── limo_original_1000.jsonl      # Original LIMO data")
    print("    └── limo_code_switched_1000.jsonl # Code-switched data")
    print("  ./models/")
    print("    ├── baseline_model/               # Model trained on original")
    print("    └── code_switched_model/          # Model trained on code-switched")

    print("\n" + "=" * 60)
    print("COST ESTIMATION (o4-mini-2025-04-16 with Batch API)")
    print("=" * 60)

    print("\n✅ GOOD NEWS: o4-mini with Batch API is much more affordable!")
    print("\nOpenAI o4-mini Batch API costs (approximate):")
    print("  • 10 samples:   ~$0.15 - $0.50")
    print("  • 100 samples:  ~$1.50 - $5.00")
    print("  • 1000 samples: ~$15 - $50")
    print("\n💰 Cost breakdown (Batch API - 50% discount):")
    print("  • Input: $0.55 per 1M tokens")
    print("  • Output: $2.20 per 1M tokens")
    print("  • Each solution ~2K-8K tokens total")
    print("\n⏱️  TIME: Batch processing (much faster overall)")
    print("  • 10 samples: ~5-15 minutes (batch processing)")
    print("  • 100 samples: ~15-30 minutes (batch processing)")
    print("  • 1000 samples: ~1-3 hours (batch processing)")
    print("\n🚀 Batch API benefits: 50% cost savings + faster overall processing")
    print("(Actual costs/time depend on solution complexity)")

    print("\n" + "=" * 60)

    while True:
        choice = input(
            "\nWhat would you like to do?\n"
            "1. Test with 10 samples (~$0.15-0.50, 5-15 min)\n"
            "2. Medium test: 100 samples (~$1.50-5.00, 15-30 min)\n"
            "3. Full pipeline: 1000 samples (~$15-50, 1-3 hours)\n"
            "4. Custom command\n"
            "5. Exit\n"
            "Choice (1-5): "
        ).strip()

        if choice == "1":
            print("\n🚀 Running test with 10 samples...")
            print("💰 Cost: ~$0.15-0.50 (very affordable!)")
            print("⏱️  Time: ~5-15 minutes (batch processing)")
            confirm = input("Continue? (y/N): ")
            if confirm.lower() == "y":
                cmd = "python run_pipeline.py --num_samples 10 --use_batch_api"
                print(f"Command: {cmd}")
                os.system(cmd)
            break

        elif choice == "2":
            print("\n🚀 Running medium test with 100 samples...")
            print("💰 Cost: ~$1.50-5.00 (very reasonable!)")
            print("⏱️  Time: ~15-30 minutes (batch processing)")
            confirm = input("Continue? (y/N): ")
            if confirm.lower() == "y":
                cmd = "python run_pipeline.py --num_samples 100 --use_batch_api"
                print(f"Command: {cmd}")
                os.system(cmd)
            break

        elif choice == "3":
            print("\n� Running full pipeline with 1000 samples...")
            print("💰 Cost: ~$15-50 (very affordable compared to o3-pro!)")
            print("⏱️  Time: ~1-3 hours (batch processing)")
            print("✅ This is now very reasonable with o4-mini + Batch API!")
            confirm = input("Continue? (y/N): ")
            if confirm.lower() == "y":
                print("\n🚀 Running full pipeline...")
                cmd = "python run_pipeline.py --num_samples 1000 --use_batch_api"
                print(f"Command: {cmd}")
                os.system(cmd)
            break

        elif choice == "4":
            print("\nCustom commands:")
            print("python create_code_switched_data.py --help")
            print("python run_pipeline.py --help")
            print("python finetune.py --help")
            break

        elif choice == "5":
            print("Goodbye!")
            break

        else:
            print("Invalid choice. Please select 1-5.")


if __name__ == "__main__":
    main()
