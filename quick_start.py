"""
Quick start script for KULLM-Pro code-switching pipeline
"""

import os
import sys
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def check_requirements():
    """Check if all requirements are met"""
    logger.info("Checking requirements...")

    # Check OpenAI API key
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        logger.error("OpenAI API key not found!")
        logger.error("Please set your OpenAI API key:")
        logger.error("  export OPENAI_API_KEY='your-api-key-here'")
        return False

    logger.info("✓ OpenAI API key found")

    # Check system prompt file
    if not os.path.exists("system_prompt.txt"):
        logger.error("system_prompt.txt not found!")
        return False

    logger.info("✓ System prompt file found")

    # Check if required Python packages are available
    try:
        import openai
        import datasets
        import transformers

        logger.info("✓ Required packages available")
    except ImportError as e:
        logger.error(f"Missing required package: {e}")
        logger.error("Please install requirements: pip install -r requirements.txt")
        return False

    return True


def main():
    """Main quick start function"""
    print("=" * 60)
    print("KULLM-Pro Code-Switching Pipeline - Quick Start")
    print("=" * 60)

    if not check_requirements():
        print("\n❌ Requirements check failed. Please fix the issues above.")
        sys.exit(1)

    print("\n✅ All requirements met!")

    print("\n" + "=" * 60)
    print("QUICK START OPTIONS")
    print("=" * 60)

    print("\n1. Create datasets only (recommended first step):")
    print("   python create_code_switched_data.py --num_samples 100")

    print("\n2. Run complete pipeline (datasets + training):")
    print("   python run_pipeline.py --num_samples 100")

    print("\n3. Train on existing data:")
    print("   python finetune.py --train_file ./data/limo_original_1000.jsonl")

    print("\n4. Test with small sample:")
    print("   python run_pipeline.py --num_samples 10 --max_concurrent 1")

    print("\n" + "=" * 60)
    print("IMPORTANT NOTES")
    print("=" * 60)

    print("\n• Start with a small number of samples (10-100) for testing")
    print("• Full 1000 samples will take several hours and cost ~$20-50")
    print("• Monitor your OpenAI API usage and costs")
    print("• The pipeline creates two models: baseline and code-switched")

    print("\n" + "=" * 60)
    print("FILE STRUCTURE")
    print("=" * 60)

    print("\nAfter running, you'll have:")
    print("  ./data/")
    print("    ├── limo_original_1000.jsonl      # Original LIMO data")
    print("    └── limo_code_switched_1000.jsonl # Code-switched data")
    print("  ./models/")
    print("    ├── baseline_model/               # Model trained on original")
    print("    └── code_switched_model/          # Model trained on code-switched")

    print("\n" + "=" * 60)
    print("COST ESTIMATION")
    print("=" * 60)

    print("\nOpenAI o1-mini costs (approximate):")
    print("  • 10 samples:   ~$0.50 - $2")
    print("  • 100 samples:  ~$5 - $15")
    print("  • 1000 samples: ~$20 - $50")
    print("\n(Actual costs depend on solution length and complexity)")

    print("\n" + "=" * 60)

    while True:
        choice = input(
            "\nWhat would you like to do?\n"
            "1. Test with 10 samples\n"
            "2. Create 100 samples\n"
            "3. Full pipeline (1000 samples)\n"
            "4. Custom command\n"
            "5. Exit\n"
            "Choice (1-5): "
        ).strip()

        if choice == "1":
            print("\n🚀 Running test with 10 samples...")
            cmd = "python run_pipeline.py --num_samples 10 --max_concurrent 1 --requests_per_minute 10"
            print(f"Command: {cmd}")
            os.system(cmd)
            break

        elif choice == "2":
            print("\n🚀 Creating 100 samples...")
            cmd = "python run_pipeline.py --num_samples 100 --max_concurrent 2 --requests_per_minute 20"
            print(f"Command: {cmd}")
            os.system(cmd)
            break

        elif choice == "3":
            confirm = input(
                "⚠️  This will cost ~$20-50 and take several hours. Continue? (y/N): "
            )
            if confirm.lower() == "y":
                print("\n🚀 Running full pipeline...")
                cmd = "python run_pipeline.py --num_samples 1000"
                print(f"Command: {cmd}")
                os.system(cmd)
            break

        elif choice == "4":
            print("\nCustom commands:")
            print("python create_code_switched_data.py --help")
            print("python run_pipeline.py --help")
            print("python finetune.py --help")
            break

        elif choice == "5":
            print("Goodbye!")
            break

        else:
            print("Invalid choice. Please select 1-5.")


if __name__ == "__main__":
    main()
