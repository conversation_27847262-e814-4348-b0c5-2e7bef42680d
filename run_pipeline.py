"""
Complete pipeline to create code-switched LIMO dataset and train models
"""

import os
import sys
import asyncio
import logging
import argparse
import subprocess
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def run_command(command, description=""):
    """Run a shell command and handle errors"""
    logger.info(f"Running: {description or command}")

    try:
        result = subprocess.run(
            command, shell=True, check=True, capture_output=True, text=True
        )

        if result.stdout:
            logger.info(f"Output: {result.stdout}")

        return True

    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed: {command}")
        logger.error(f"Error: {e.stderr}")
        return False


async def create_datasets(args):
    """Create both original and code-switched datasets"""
    logger.info("=== Creating Datasets ===")

    # Create data directory
    os.makedirs(args.data_dir, exist_ok=True)

    # Run the code-switching script
    cmd_args = [
        "python",
        "create_code_switched_data.py",
        "--num_samples",
        str(args.num_samples),
        "--output_dir",
        args.data_dir,
        "--system_prompt",
        args.system_prompt,
        "--max_concurrent",
        str(args.max_concurrent),
        "--requests_per_minute",
        str(args.requests_per_minute),
    ]

    if args.api_key:
        cmd_args.extend(["--api_key", args.api_key])

    if args.use_batch_api:
        cmd_args.append("--use_batch_api")
        cmd_args.extend(["--batch_size", str(args.batch_size)])

    if args.skip_download:
        cmd_args.append("--skip_download")

    if args.original_data_file:
        cmd_args.extend(["--original_data_file", args.original_data_file])

    # Run the command
    cmd = " ".join(cmd_args)
    success = run_command(cmd, "Creating datasets with OpenAI code-switching")

    if not success:
        logger.error("Failed to create datasets")
        return False

    return True


def train_model(train_file, output_dir, model_name="baseline"):
    """Train a single model"""
    logger.info(f"=== Training {model_name} Model ===")

    cmd_args = [
        "python",
        "finetune.py",
        "--train_file",
        train_file,
        "--output_dir",
        output_dir,
        "--epochs",
        "3",
        "--batch_size",
        "2",
        "--learning_rate",
        "2e-4",
    ]

    cmd = " ".join(cmd_args)
    success = run_command(cmd, f"Training {model_name} model")

    return success


def main():
    """Main pipeline function"""
    parser = argparse.ArgumentParser(
        description="Complete LIMO code-switching pipeline"
    )

    # Data creation arguments
    parser.add_argument(
        "--num_samples", type=int, default=1000, help="Number of samples to process"
    )
    parser.add_argument("--data_dir", type=str, default="./data", help="Data directory")
    parser.add_argument(
        "--system_prompt",
        type=str,
        default="system_prompt.txt",
        help="System prompt file",
    )
    parser.add_argument("--api_key", type=str, help="OpenAI API key")
    parser.add_argument(
        "--use_batch_api",
        action="store_true",
        help="Use OpenAI Batch API for cost savings",
    )
    parser.add_argument(
        "--batch_size", type=int, default=100, help="Batch size for Batch API"
    )
    parser.add_argument(
        "--max_concurrent",
        type=int,
        default=10,
        help="Max concurrent OpenAI requests (regular API only)",
    )
    parser.add_argument(
        "--requests_per_minute",
        type=int,
        default=1000,
        help="OpenAI requests per minute (regular API only)",
    )
    parser.add_argument(
        "--skip_download", action="store_true", help="Skip downloading LIMO data"
    )
    parser.add_argument(
        "--original_data_file", type=str, help="Existing original data file"
    )

    # Training arguments
    parser.add_argument(
        "--models_dir", type=str, default="./models", help="Models output directory"
    )
    parser.add_argument(
        "--skip_data_creation", action="store_true", help="Skip data creation step"
    )
    parser.add_argument(
        "--skip_training", action="store_true", help="Skip training step"
    )
    parser.add_argument(
        "--train_baseline_only", action="store_true", help="Train only baseline model"
    )
    parser.add_argument(
        "--train_code_switched_only",
        action="store_true",
        help="Train only code-switched model",
    )

    args = parser.parse_args()

    # Validate arguments
    if (
        not args.skip_data_creation
        and not args.api_key
        and not os.getenv("OPENAI_API_KEY")
    ):
        logger.error(
            "OpenAI API key required for data creation. Set --api_key or OPENAI_API_KEY environment variable."
        )
        sys.exit(1)

    # Define file paths
    original_file = os.path.join(args.data_dir, "limo_original_1000.jsonl")
    code_switched_file = os.path.join(args.data_dir, "limo_code_switched_1000.jsonl")

    baseline_model_dir = os.path.join(args.models_dir, "baseline_model")
    code_switched_model_dir = os.path.join(args.models_dir, "code_switched_model")

    # Create directories
    os.makedirs(args.models_dir, exist_ok=True)

    try:
        # Step 1: Create datasets
        if not args.skip_data_creation:
            success = asyncio.run(create_datasets(args))
            if not success:
                logger.error("Dataset creation failed")
                sys.exit(1)
        else:
            logger.info("Skipping data creation")

        # Verify files exist
        if not os.path.exists(original_file):
            logger.error(f"Original data file not found: {original_file}")
            sys.exit(1)

        if not args.train_baseline_only and not os.path.exists(code_switched_file):
            logger.error(f"Code-switched data file not found: {code_switched_file}")
            sys.exit(1)

        # Step 2: Train models
        if not args.skip_training:

            # Train baseline model
            if not args.train_code_switched_only:
                logger.info("Training baseline model on original LIMO data...")
                success = train_model(original_file, baseline_model_dir, "baseline")
                if not success:
                    logger.error("Baseline model training failed")
                    sys.exit(1)

            # Train code-switched model
            if not args.train_baseline_only:
                logger.info("Training code-switched model...")
                success = train_model(
                    code_switched_file, code_switched_model_dir, "code-switched"
                )
                if not success:
                    logger.error("Code-switched model training failed")
                    sys.exit(1)

        else:
            logger.info("Skipping training")

        # Summary
        logger.info("=== Pipeline Complete ===")
        logger.info(f"Data files:")
        logger.info(f"  Original: {original_file}")
        logger.info(f"  Code-switched: {code_switched_file}")

        if not args.skip_training:
            logger.info(f"Trained models:")
            if not args.train_code_switched_only:
                logger.info(f"  Baseline: {baseline_model_dir}")
            if not args.train_baseline_only:
                logger.info(f"  Code-switched: {code_switched_model_dir}")

        logger.info("Pipeline completed successfully!")

    except KeyboardInterrupt:
        logger.info("Pipeline interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Pipeline failed with error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
