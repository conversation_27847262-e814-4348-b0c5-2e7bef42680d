#!/usr/bin/env python3
"""
Script to run both baseline and code-switched model training
"""

import subprocess
import sys
import logging
from datetime import datetime
import argparse

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_command(command, description=""):
    """Run a shell command and handle errors"""
    logger.info(f"Running: {description or command}")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            text=True,
            capture_output=False  # Show output in real-time
        )
        
        logger.info(f"✅ {description} completed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ {description} failed with return code {e.returncode}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Run both baseline and code-switched training")
    parser.add_argument("--epochs", type=int, default=3, help="Number of epochs")
    parser.add_argument("--batch_size", type=int, default=2, help="Batch size")
    parser.add_argument("--learning_rate", type=float, default=2e-4, help="Learning rate")
    parser.add_argument("--no_wandb", action="store_true", help="Disable WandB logging")
    parser.add_argument("--baseline_only", action="store_true", help="Train only baseline model")
    parser.add_argument("--code_switched_only", action="store_true", help="Train only code-switched model")
    
    args = parser.parse_args()
    
    # Data files
    baseline_data = "./data/limo_original_1000_synced.jsonl"
    code_switched_data = "./data/limo_code_switched_1000_synced.jsonl"
    
    # Output directories
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    baseline_output = f"./models/baseline_{timestamp}"
    code_switched_output = f"./models/code_switched_{timestamp}"
    
    # Common training arguments
    common_args = [
        f"--epochs {args.epochs}",
        f"--batch_size {args.batch_size}",
        f"--learning_rate {args.learning_rate}",
        "--no_wandb" if args.no_wandb else "",
    ]
    common_args_str = " ".join(filter(None, common_args))
    
    logger.info("=" * 60)
    logger.info("KOREAN-ENGLISH CODE-SWITCHED MATH TRAINING")
    logger.info("=" * 60)
    logger.info(f"Epochs: {args.epochs}")
    logger.info(f"Batch size: {args.batch_size}")
    logger.info(f"Learning rate: {args.learning_rate}")
    logger.info(f"WandB enabled: {not args.no_wandb}")
    logger.info(f"Timestamp: {timestamp}")
    
    success_count = 0
    total_jobs = 0
    
    # Train baseline model
    if not args.code_switched_only:
        total_jobs += 1
        logger.info("\n" + "=" * 40)
        logger.info("1. TRAINING BASELINE MODEL")
        logger.info("=" * 40)
        
        baseline_cmd = (
            f"python train.py "
            f"--train_file {baseline_data} "
            f"--output_dir {baseline_output} "
            f"--run_name baseline_{timestamp} "
            f"{common_args_str}"
        )
        
        if run_command(baseline_cmd, "Baseline model training"):
            success_count += 1
            logger.info(f"✅ Baseline model saved to: {baseline_output}")
        else:
            logger.error("❌ Baseline training failed")
            if not args.baseline_only:
                logger.info("Continuing with code-switched training...")
    
    # Train code-switched model
    if not args.baseline_only:
        total_jobs += 1
        logger.info("\n" + "=" * 40)
        logger.info("2. TRAINING CODE-SWITCHED MODEL")
        logger.info("=" * 40)
        
        code_switched_cmd = (
            f"python train.py "
            f"--train_file {code_switched_data} "
            f"--output_dir {code_switched_output} "
            f"--run_name code_switched_{timestamp} "
            f"{common_args_str}"
        )
        
        if run_command(code_switched_cmd, "Code-switched model training"):
            success_count += 1
            logger.info(f"✅ Code-switched model saved to: {code_switched_output}")
        else:
            logger.error("❌ Code-switched training failed")
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("TRAINING SUMMARY")
    logger.info("=" * 60)
    logger.info(f"Completed jobs: {success_count}/{total_jobs}")
    
    if success_count == total_jobs:
        logger.info("🎉 All training jobs completed successfully!")
        logger.info("\nTrained models:")
        if not args.code_switched_only:
            logger.info(f"  📁 Baseline: {baseline_output}")
        if not args.baseline_only:
            logger.info(f"  📁 Code-switched: {code_switched_output}")
        logger.info("\nNext steps:")
        logger.info("  1. Evaluate both models on test data")
        logger.info("  2. Compare performance metrics")
        logger.info("  3. Analyze code-switching effectiveness")
    else:
        logger.error(f"❌ {total_jobs - success_count} training job(s) failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
