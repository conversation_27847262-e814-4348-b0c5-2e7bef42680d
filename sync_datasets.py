"""
Script to synchronize original and code-switched datasets
Ensures both datasets contain exactly the same samples for fair comparison
"""

import json
import logging
import argparse
import os
from typing import Set, List, Dict, Any, Tuple

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_jsonl(file_path: str) -> List[Dict[str, Any]]:
    """Load data from JSONL file"""
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if line:
                try:
                    data.append(json.loads(line))
                except json.JSONDecodeError as e:
                    logger.warning(f"Skipping invalid JSON on line {line_num}: {e}")
    return data

def save_jsonl(data: List[Dict[str, Any]], file_path: str):
    """Save data to JSONL file"""
    with open(file_path, 'w', encoding='utf-8') as f:
        for item in data:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')

def extract_question_from_original(item: Dict[str, Any]) -> str:
    """Extract question from original dataset format"""
    messages = item.get('messages', [])
    for msg in messages:
        if msg.get('role') == 'user':
            content = msg.get('content', '')
            if 'Problem: ' in content:
                return content.split('Problem: ')[-1].strip()
    return ""

def extract_question_from_code_switched(item: Dict[str, Any]) -> str:
    """Extract question from code-switched dataset format"""
    messages = item.get('messages', [])
    for msg in messages:
        if msg.get('role') == 'user':
            content = msg.get('content', '')
            if 'Problem: ' in content:
                return content.split('Problem: ')[-1].strip()
    return ""

def create_question_to_item_map(data: List[Dict[str, Any]], extract_func) -> Dict[str, Dict[str, Any]]:
    """Create a mapping from question text to dataset item"""
    question_map = {}
    for item in data:
        question = extract_func(item)
        if question:
            question_map[question] = item
    return question_map

def sync_datasets(original_file: str, code_switched_file: str) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """Synchronize datasets to contain exactly the same questions"""
    
    # Load both datasets
    logger.info("Loading datasets...")
    original_data = load_jsonl(original_file)
    code_switched_data = load_jsonl(code_switched_file)
    
    logger.info(f"Original dataset: {len(original_data)} samples")
    logger.info(f"Code-switched dataset: {len(code_switched_data)} samples")
    
    # Create question mappings
    logger.info("Creating question mappings...")
    original_map = create_question_to_item_map(original_data, extract_question_from_original)
    code_switched_map = create_question_to_item_map(code_switched_data, extract_question_from_code_switched)
    
    logger.info(f"Original questions mapped: {len(original_map)}")
    logger.info(f"Code-switched questions mapped: {len(code_switched_map)}")
    
    # Find common questions
    original_questions = set(original_map.keys())
    code_switched_questions = set(code_switched_map.keys())
    
    common_questions = original_questions & code_switched_questions
    only_in_original = original_questions - code_switched_questions
    only_in_code_switched = code_switched_questions - original_questions
    
    logger.info(f"Common questions: {len(common_questions)}")
    logger.info(f"Only in original: {len(only_in_original)}")
    logger.info(f"Only in code-switched: {len(only_in_code_switched)}")
    
    if only_in_original:
        logger.info("Sample questions only in original:")
        for i, q in enumerate(list(only_in_original)[:3]):
            logger.info(f"  {i+1}. {q[:100]}...")
    
    if only_in_code_switched:
        logger.info("Sample questions only in code-switched:")
        for i, q in enumerate(list(only_in_code_switched)[:3]):
            logger.info(f"  {i+1}. {q[:100]}...")
    
    # Create synchronized datasets with common questions only
    synced_original = []
    synced_code_switched = []
    
    for question in sorted(common_questions):  # Sort for consistent ordering
        synced_original.append(original_map[question])
        synced_code_switched.append(code_switched_map[question])
    
    logger.info(f"Synchronized datasets: {len(synced_original)} samples each")
    
    return synced_original, synced_code_switched

def main():
    parser = argparse.ArgumentParser(description="Synchronize original and code-switched datasets")
    parser.add_argument("--original_file", type=str, default="./data/limo_original_1000.jsonl",
                       help="Path to original dataset file")
    parser.add_argument("--code_switched_file", type=str, default="./data/limo_code_switched_1000.jsonl",
                       help="Path to code-switched dataset file")
    parser.add_argument("--output_dir", type=str, default="./data",
                       help="Output directory for synchronized files")
    parser.add_argument("--suffix", type=str, default="_synced",
                       help="Suffix to add to synchronized filenames")
    
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Synchronize datasets
    logger.info("=" * 60)
    logger.info("SYNCHRONIZING DATASETS")
    logger.info("=" * 60)
    
    synced_original, synced_code_switched = sync_datasets(args.original_file, args.code_switched_file)
    
    # Generate output filenames
    original_basename = os.path.splitext(os.path.basename(args.original_file))[0]
    code_switched_basename = os.path.splitext(os.path.basename(args.code_switched_file))[0]
    
    synced_original_file = os.path.join(args.output_dir, f"{original_basename}{args.suffix}.jsonl")
    synced_code_switched_file = os.path.join(args.output_dir, f"{code_switched_basename}{args.suffix}.jsonl")
    
    # Save synchronized datasets
    logger.info("Saving synchronized datasets...")
    save_jsonl(synced_original, synced_original_file)
    save_jsonl(synced_code_switched, synced_code_switched_file)
    
    # Verification
    logger.info("=" * 60)
    logger.info("VERIFICATION")
    logger.info("=" * 60)
    
    if len(synced_original) == len(synced_code_switched):
        logger.info(f"✅ SUCCESS: Both datasets have {len(synced_original)} samples")
        logger.info(f"✅ Datasets are now synchronized for fair comparison")
    else:
        logger.error(f"❌ ERROR: Sample count mismatch!")
        logger.error(f"   Original: {len(synced_original)} samples")
        logger.error(f"   Code-switched: {len(synced_code_switched)} samples")
        return
    
    # Summary
    logger.info("=" * 60)
    logger.info("SUMMARY")
    logger.info("=" * 60)
    logger.info(f"Synchronized samples: {len(synced_original)}")
    logger.info(f"Success rate: {len(synced_original) / 1000 * 100:.1f}% of original 1000 samples")
    logger.info("")
    logger.info("Synchronized files:")
    logger.info(f"  Original: {synced_original_file}")
    logger.info(f"  Code-switched: {synced_code_switched_file}")
    logger.info("")
    logger.info("Next steps for training:")
    logger.info(f"  1. Baseline model:")
    logger.info(f"     python finetune.py --train_file {synced_original_file} --output_dir ./models/baseline_synced")
    logger.info(f"  2. Code-switched model:")
    logger.info(f"     python finetune.py --train_file {synced_code_switched_file} --output_dir ./models/code_switched_synced")

if __name__ == "__main__":
    main()
