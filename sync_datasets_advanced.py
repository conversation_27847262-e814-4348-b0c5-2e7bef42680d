"""
Advanced script to synchronize original and code-switched datasets
Handles different data formats and ensures exact matching
"""

import json
import logging
import argparse
import os
import re
from typing import Set, List, Dict, Any, Tuple

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_jsonl(file_path: str) -> List[Dict[str, Any]]:
    """Load data from JSONL file"""
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if line:
                try:
                    data.append(json.loads(line))
                except json.JSONDecodeError as e:
                    logger.warning(f"Skipping invalid JSON on line {line_num}: {e}")
    return data

def save_jsonl(data: List[Dict[str, Any]], file_path: str):
    """Save data to JSONL file"""
    with open(file_path, 'w', encoding='utf-8') as f:
        for item in data:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')

def extract_question_from_original(item: Dict[str, Any]) -> str:
    """Extract question from original dataset format"""
    messages = item.get('messages', [])
    for msg in messages:
        if msg.get('role') == 'user':
            content = msg.get('content', '')
            if 'Problem: ' in content:
                return content.split('Problem: ')[-1].strip()
    return ""

def extract_question_from_code_switched(item: Dict[str, Any]) -> str:
    """Extract question from code-switched dataset (batch API format)"""
    try:
        # Handle batch API response format
        if 'response' in item and 'body' in item['response']:
            # This is batch API format - we need to reconstruct the original question
            # from the custom_id and match it with the original dataset
            custom_id = item.get('custom_id', '')
            if custom_id.startswith('request-'):
                index = int(custom_id.split('-')[1])
                return f"INDEX_{index}"  # Use index as identifier
        
        # Handle regular messages format
        messages = item.get('messages', [])
        for msg in messages:
            if msg.get('role') == 'user':
                content = msg.get('content', '')
                if 'Problem: ' in content:
                    return content.split('Problem: ')[-1].strip()
    except Exception as e:
        logger.warning(f"Error extracting question from code-switched item: {e}")
    
    return ""

def convert_code_switched_to_messages_format(item: Dict[str, Any], original_question: str, original_answer: str) -> Dict[str, Any]:
    """Convert batch API format to messages format"""
    try:
        if 'response' in item and 'body' in item['response']:
            # Extract the code-switched solution from batch response
            choices = item['response']['body'].get('choices', [])
            if choices:
                code_switched_solution = choices[0]['message']['content']
                
                # Create messages format
                return {
                    "messages": [
                        {
                            "role": "system",
                            "content": "You are a helpful assistant that solves mathematical problems step by step."
                        },
                        {
                            "role": "user",
                            "content": f"Solve the following mathematical problem step by step. Provide a detailed solution and then give the final answer.\n\nProblem: {original_question}"
                        },
                        {
                            "role": "assistant",
                            "content": f"{code_switched_solution}\n\nFinal Answer: {original_answer}"
                        }
                    ]
                }
    except Exception as e:
        logger.warning(f"Error converting code-switched item: {e}")
    
    return item

def sync_datasets_by_index(original_file: str, code_switched_file: str) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """Synchronize datasets using index-based matching"""
    
    # Load both datasets
    logger.info("Loading datasets...")
    original_data = load_jsonl(original_file)
    code_switched_data = load_jsonl(code_switched_file)
    
    logger.info(f"Original dataset: {len(original_data)} samples")
    logger.info(f"Code-switched dataset: {len(code_switched_data)} samples")
    
    # Create index mapping for code-switched data
    code_switched_by_index = {}
    for item in code_switched_data:
        try:
            custom_id = item.get('custom_id', '')
            if custom_id.startswith('request-'):
                index = int(custom_id.split('-')[1])
                code_switched_by_index[index] = item
        except Exception as e:
            logger.warning(f"Error processing code-switched item: {e}")
    
    logger.info(f"Code-switched items by index: {len(code_switched_by_index)}")
    
    # Create synchronized datasets
    synced_original = []
    synced_code_switched = []
    
    for i, original_item in enumerate(original_data):
        if i in code_switched_by_index:
            # Extract question and answer from original
            question = extract_question_from_original(original_item)
            
            # Extract answer from original
            messages = original_item.get('messages', [])
            answer = ""
            for msg in messages:
                if msg.get('role') == 'assistant':
                    content = msg.get('content', '')
                    if 'Final Answer: ' in content:
                        answer = content.split('Final Answer: ')[-1].strip()
                    break
            
            # Convert code-switched item to proper format
            code_switched_item = convert_code_switched_to_messages_format(
                code_switched_by_index[i], question, answer
            )
            
            if code_switched_item.get('messages'):
                synced_original.append(original_item)
                synced_code_switched.append(code_switched_item)
        else:
            logger.debug(f"No code-switched version for index {i}")
    
    logger.info(f"Synchronized datasets: {len(synced_original)} samples each")
    
    return synced_original, synced_code_switched

def main():
    parser = argparse.ArgumentParser(description="Advanced synchronization of original and code-switched datasets")
    parser.add_argument("--original_file", type=str, default="./data/limo_original_1000.jsonl",
                       help="Path to original dataset file")
    parser.add_argument("--code_switched_file", type=str, default="./data/limo_code_switched_1000.jsonl",
                       help="Path to code-switched dataset file")
    parser.add_argument("--output_dir", type=str, default="./data",
                       help="Output directory for synchronized files")
    parser.add_argument("--suffix", type=str, default="_synced",
                       help="Suffix to add to synchronized filenames")
    
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Synchronize datasets
    logger.info("=" * 60)
    logger.info("ADVANCED DATASET SYNCHRONIZATION")
    logger.info("=" * 60)
    
    synced_original, synced_code_switched = sync_datasets_by_index(
        args.original_file, args.code_switched_file
    )
    
    # Generate output filenames
    original_basename = os.path.splitext(os.path.basename(args.original_file))[0]
    code_switched_basename = os.path.splitext(os.path.basename(args.code_switched_file))[0]
    
    synced_original_file = os.path.join(args.output_dir, f"{original_basename}{args.suffix}.jsonl")
    synced_code_switched_file = os.path.join(args.output_dir, f"{code_switched_basename}{args.suffix}.jsonl")
    
    # Save synchronized datasets
    logger.info("Saving synchronized datasets...")
    save_jsonl(synced_original, synced_original_file)
    save_jsonl(synced_code_switched, synced_code_switched_file)
    
    # Verification
    logger.info("=" * 60)
    logger.info("VERIFICATION")
    logger.info("=" * 60)
    
    if len(synced_original) == len(synced_code_switched):
        logger.info(f"✅ SUCCESS: Both datasets have {len(synced_original)} samples")
        logger.info(f"✅ Datasets are now synchronized for fair comparison")
    else:
        logger.error(f"❌ ERROR: Sample count mismatch!")
        logger.error(f"   Original: {len(synced_original)} samples")
        logger.error(f"   Code-switched: {len(synced_code_switched)} samples")
        return
    
    # Summary
    logger.info("=" * 60)
    logger.info("SUMMARY")
    logger.info("=" * 60)
    logger.info(f"Synchronized samples: {len(synced_original)}")
    logger.info(f"Success rate: {len(synced_original) / 1000 * 100:.1f}% of original 1000 samples")
    logger.info("")
    logger.info("Synchronized files:")
    logger.info(f"  Original: {synced_original_file}")
    logger.info(f"  Code-switched: {synced_code_switched_file}")
    logger.info("")
    logger.info("Next steps for training:")
    logger.info(f"  1. Baseline model:")
    logger.info(f"     python finetune.py --train_file {synced_original_file} --output_dir ./models/baseline_synced")
    logger.info(f"  2. Code-switched model:")
    logger.info(f"     python finetune.py --train_file {synced_code_switched_file} --output_dir ./models/code_switched_synced")

if __name__ == "__main__":
    main()
