"""
Test script to verify KULLM-Pro installation and basic functionality
"""

import sys
import logging
import traceback
from typing import List, Tuple

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_imports() -> Tuple[bool, List[str]]:
    """Test if all required packages can be imported"""
    logger.info("Testing package imports...")
    
    required_packages = [
        "torch",
        "transformers", 
        "datasets",
        "accelerate",
        "peft",
        "bitsandbytes",
        "numpy",
        "pandas",
        "sklearn",
        "tqdm"
    ]
    
    failed_imports = []
    
    for package in required_packages:
        try:
            __import__(package)
            logger.info(f"✓ {package}")
        except ImportError as e:
            logger.error(f"✗ {package}: {e}")
            failed_imports.append(package)
    
    return len(failed_imports) == 0, failed_imports

def test_cuda_availability():
    """Test CUDA availability"""
    logger.info("Testing CUDA availability...")
    
    try:
        import torch
        if torch.cuda.is_available():
            logger.info(f"✓ CUDA available: {torch.cuda.device_count()} GPU(s)")
            logger.info(f"✓ Current device: {torch.cuda.current_device()}")
            logger.info(f"✓ Device name: {torch.cuda.get_device_name()}")
            return True
        else:
            logger.warning("✗ CUDA not available - training will be slow on CPU")
            return False
    except Exception as e:
        logger.error(f"✗ Error checking CUDA: {e}")
        return False

def test_config_loading():
    """Test configuration loading"""
    logger.info("Testing configuration loading...")
    
    try:
        from config import Config
        config = Config()
        
        logger.info(f"✓ Model name: {config.model.model_name}")
        logger.info(f"✓ Max length: {config.model.max_length}")
        logger.info(f"✓ LoRA rank: {config.lora.r}")
        logger.info(f"✓ Training epochs: {config.training.num_train_epochs}")
        logger.info(f"✓ Output directory: {config.training.output_dir}")
        
        return True
    except Exception as e:
        logger.error(f"✗ Error loading config: {e}")
        traceback.print_exc()
        return False

def test_data_processor():
    """Test data processor initialization"""
    logger.info("Testing data processor...")
    
    try:
        from config import Config
        from data_preprocessing import LIMODataProcessor
        
        config = Config()
        processor = LIMODataProcessor(config)
        
        logger.info("✓ Data processor initialized successfully")
        return True
    except Exception as e:
        logger.error(f"✗ Error initializing data processor: {e}")
        traceback.print_exc()
        return False

def test_model_setup():
    """Test model setup utilities"""
    logger.info("Testing model setup utilities...")
    
    try:
        from config import Config
        from training_utils import ModelSetup
        
        config = Config()
        model_setup = ModelSetup(config)
        
        logger.info("✓ Model setup initialized successfully")
        return True
    except Exception as e:
        logger.error(f"✗ Error initializing model setup: {e}")
        traceback.print_exc()
        return False

def test_dataset_access():
    """Test dataset access (without downloading)"""
    logger.info("Testing dataset access...")
    
    try:
        from datasets import load_dataset_builder
        
        # Just check if we can access the dataset info without downloading
        builder = load_dataset_builder("GAIR/LIMO")
        info = builder.info
        
        logger.info(f"✓ Dataset accessible: {info.dataset_name}")
        logger.info(f"✓ Dataset description: {info.description[:100]}...")
        
        return True
    except Exception as e:
        logger.error(f"✗ Error accessing dataset: {e}")
        return False

def run_all_tests():
    """Run all tests"""
    logger.info("=" * 50)
    logger.info("KULLM-Pro Installation Test")
    logger.info("=" * 50)
    
    tests = [
        ("Package Imports", test_imports),
        ("CUDA Availability", test_cuda_availability),
        ("Configuration Loading", test_config_loading),
        ("Data Processor", test_data_processor),
        ("Model Setup", test_model_setup),
        ("Dataset Access", test_dataset_access),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} ---")
        try:
            if test_name == "Package Imports":
                success, failed = test_func()
                results.append((test_name, success))
                if not success:
                    logger.error(f"Failed imports: {failed}")
            else:
                success = test_func()
                results.append((test_name, success))
        except Exception as e:
            logger.error(f"Test {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("TEST SUMMARY")
    logger.info("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "PASS" if success else "FAIL"
        logger.info(f"{test_name}: {status}")
        if success:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! KULLM-Pro is ready to use.")
        return True
    else:
        logger.warning("⚠️  Some tests failed. Please check the installation.")
        return False

def main():
    """Main function"""
    success = run_all_tests()
    
    if success:
        logger.info("\n" + "=" * 50)
        logger.info("NEXT STEPS")
        logger.info("=" * 50)
        logger.info("1. Run basic fine-tuning:")
        logger.info("   python finetune.py")
        logger.info("")
        logger.info("2. Or try the example script:")
        logger.info("   python example_usage.py")
        logger.info("")
        logger.info("3. For help:")
        logger.info("   python finetune.py --help")
        
        sys.exit(0)
    else:
        logger.error("\nPlease fix the installation issues before proceeding.")
        sys.exit(1)

if __name__ == "__main__":
    main()
