"""
Simple test script to verify training setup
"""

import os
import sys
import json

def test_imports():
    """Test if all required packages can be imported"""
    print("Testing imports...")
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
    except ImportError as e:
        print(f"❌ PyTorch import failed: {e}")
        return False
    
    try:
        import transformers
        print(f"✅ Transformers: {transformers.__version__}")
    except ImportError as e:
        print(f"❌ Transformers import failed: {e}")
        return False
    
    try:
        import peft
        print(f"✅ PEFT: {peft.__version__}")
    except ImportError as e:
        print(f"❌ PEFT import failed: {e}")
        return False
    
    try:
        import datasets
        print(f"✅ Datasets: {datasets.__version__}")
    except ImportError as e:
        print(f"❌ Datasets import failed: {e}")
        return False
    
    return True

def test_data_files():
    """Test if data files exist and are valid"""
    print("\nTesting data files...")
    
    files_to_check = [
        "./data/limo_original_1000_synced.jsonl",
        "./data/limo_code_switched_1000_synced.jsonl"
    ]
    
    for file_path in files_to_check:
        if not os.path.exists(file_path):
            print(f"❌ File not found: {file_path}")
            return False
        
        try:
            with open(file_path, 'r') as f:
                lines = f.readlines()
                print(f"✅ {file_path}: {len(lines)} lines")
                
                # Test first line is valid JSON
                if lines:
                    json.loads(lines[0])
                    print(f"   First line is valid JSON")
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
            return False
    
    return True

def test_gpu():
    """Test GPU availability"""
    print("\nTesting GPU...")
    
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✅ CUDA available: {torch.cuda.device_count()} GPU(s)")
            for i in range(torch.cuda.device_count()):
                print(f"   GPU {i}: {torch.cuda.get_device_name(i)}")
        else:
            print("⚠️  CUDA not available, will use CPU")
    except Exception as e:
        print(f"❌ GPU test failed: {e}")
        return False
    
    return True

def test_model_loading():
    """Test if we can load the model"""
    print("\nTesting model loading...")
    
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        
        model_name = "Qwen/Qwen2.5-7B-Instruct"
        print(f"Loading tokenizer for {model_name}...")
        
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        print("✅ Tokenizer loaded successfully")
        
        # Don't actually load the full model to save time/memory
        print("✅ Model loading test passed (skipped full model load)")
        
    except Exception as e:
        print(f"❌ Model loading test failed: {e}")
        return False
    
    return True

def main():
    """Run all tests"""
    print("=" * 60)
    print("TRAINING ENVIRONMENT TEST")
    print("=" * 60)
    
    tests = [
        ("Import Test", test_imports),
        ("Data Files Test", test_data_files),
        ("GPU Test", test_gpu),
        ("Model Loading Test", test_model_loading),
    ]
    
    all_passed = True
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            result = test_func()
            if not result:
                all_passed = False
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("✅ ALL TESTS PASSED - Ready for training!")
        print("\nYou can now run:")
        print("python finetune.py --train_file ./data/limo_original_1000_synced.jsonl --output_dir ./models/baseline_synced --epochs 1 --batch_size 1 --learning_rate 2e-4 --no_wandb")
    else:
        print("❌ SOME TESTS FAILED - Please fix issues before training")
    print("=" * 60)

if __name__ == "__main__":
    main()
