"""
Streamlined training script for Korean-English code-switched mathematical reasoning
Fine-tunes Qwen2.5-7B-Instruct with LoRA on LIMO dataset
"""

import json
import torch
import wandb
import os
from datetime import datetime
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForSeq2Seq,
)
from peft import LoraConfig, get_peft_model, TaskType
from datasets import Dataset
import argparse
import logging

# Fix for compatibility issues
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Monkey patch for accelerate compatibility
import accelerate
if hasattr(accelerate.Accelerator, 'unwrap_model'):
    original_unwrap_model = accelerate.Accelerator.unwrap_model
    def patched_unwrap_model(self, model, keep_torch_compile=None):
        if keep_torch_compile is not None:
            # Ignore the keep_torch_compile parameter for older accelerate versions
            return original_unwrap_model(self, model)
        return original_unwrap_model(self, model)
    accelerate.Accelerator.unwrap_model = patched_unwrap_model

# Set up logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def load_jsonl_data(file_path):
    """Load data from JSONL file"""
    data = []
    with open(file_path, "r", encoding="utf-8") as f:
        for line in f:
            line = line.strip()
            if line:
                data.append(json.loads(line))
    return data


def format_conversation(example):
    """Format conversation for training with thinking model support"""
    messages = example["messages"]

    # Find system, user and assistant messages
    system_msg = ""
    user_msg = ""
    assistant_msg = ""

    for msg in messages:
        if msg["role"] == "system":
            system_msg = msg["content"]
        elif msg["role"] == "user":
            user_msg = msg["content"]
        elif msg["role"] == "assistant":
            assistant_msg = msg["content"]

    # Create input-output format with system message and thinking support
    if system_msg:
        text = f"<|im_start|>system\n{system_msg}<|im_end|>\n<|im_start|>user\n{user_msg}<|im_end|>\n<|im_start|>assistant\n{assistant_msg}<|im_end|>"
    else:
        text = f"<|im_start|>user\n{user_msg}<|im_end|>\n<|im_start|>assistant\n{assistant_msg}<|im_end|>"

    return {"text": text}


def tokenize_function(examples, tokenizer, max_length=2048):
    """Tokenize examples"""
    # Tokenize the text
    tokenized = tokenizer(
        examples["text"],
        truncation=True,
        padding=False,
        max_length=max_length,
        return_tensors=None,
    )

    # Set labels (same as input_ids for causal LM)
    tokenized["labels"] = tokenized["input_ids"].copy()

    return tokenized


def main():
    parser = argparse.ArgumentParser(
        description="Train Qwen2.5-7B on Korean-English code-switched math problems"
    )

    # Required arguments
    parser.add_argument(
        "--train_file", type=str, required=True, help="Path to training JSONL file"
    )
    parser.add_argument(
        "--output_dir", type=str, required=True, help="Output directory for model"
    )

    # Model arguments
    parser.add_argument(
        "--model_name",
        type=str,
        default="Qwen/Qwen2.5-7B-Instruct",
        help="Base model name",
    )
    parser.add_argument(
        "--max_length", type=int, default=2048, help="Maximum sequence length"
    )

    # Training arguments
    parser.add_argument(
        "--epochs", type=int, default=3, help="Number of training epochs"
    )
    parser.add_argument("--batch_size", type=int, default=2, help="Training batch size")
    parser.add_argument(
        "--learning_rate", type=float, default=2e-4, help="Learning rate"
    )
    parser.add_argument(
        "--gradient_accumulation_steps",
        type=int,
        default=8,
        help="Gradient accumulation steps",
    )

    # LoRA arguments
    parser.add_argument("--lora_r", type=int, default=16, help="LoRA rank")
    parser.add_argument("--lora_alpha", type=int, default=32, help="LoRA alpha")
    parser.add_argument("--lora_dropout", type=float, default=0.1, help="LoRA dropout")

    # Experiment tracking
    parser.add_argument(
        "--wandb_project",
        type=str,
        default="qwen-code-switched-math",
        help="WandB project name",
    )
    parser.add_argument(
        "--run_name", type=str, help="WandB run name (auto-generated if not provided)"
    )
    parser.add_argument("--no_wandb", action="store_true", help="Disable WandB logging")

    args = parser.parse_args()

    # Initialize WandB
    if not args.no_wandb:
        # Generate run name if not provided
        if not args.run_name:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            dataset_type = (
                "code_switched" if "code_switched" in args.train_file else "baseline"
            )
            args.run_name = f"{dataset_type}_{timestamp}"

        wandb.init(
            project=args.wandb_project,
            name=args.run_name,
            config={
                "model_name": args.model_name,
                "train_file": args.train_file,
                "epochs": args.epochs,
                "batch_size": args.batch_size,
                "learning_rate": args.learning_rate,
                "gradient_accumulation_steps": args.gradient_accumulation_steps,
                "lora_r": args.lora_r,
                "lora_alpha": args.lora_alpha,
                "lora_dropout": args.lora_dropout,
                "max_length": args.max_length,
            },
        )
        logger.info(f"WandB initialized: {args.wandb_project}/{args.run_name}")

    # Load and prepare data
    logger.info(f"Loading data from {args.train_file}")
    raw_data = load_jsonl_data(args.train_file)
    logger.info(f"Loaded {len(raw_data)} samples")

    # Format data
    formatted_data = [format_conversation(example) for example in raw_data]

    # Create dataset
    dataset = Dataset.from_list(formatted_data)

    # Split into train/val
    train_size = int(0.9 * len(dataset))
    train_dataset = dataset.select(range(train_size))
    val_dataset = dataset.select(range(train_size, len(dataset)))

    print(f"Train samples: {len(train_dataset)}")
    print(f"Val samples: {len(val_dataset)}")

    # Load tokenizer
    print(f"Loading tokenizer: {args.model_name}")
    tokenizer = AutoTokenizer.from_pretrained(args.model_name)

    # Add pad token if missing
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    # Tokenize datasets
    print("Tokenizing datasets...")
    train_dataset = train_dataset.map(
        lambda x: tokenize_function(x, tokenizer, args.max_length),
        batched=True,
        remove_columns=train_dataset.column_names,
    )

    val_dataset = val_dataset.map(
        lambda x: tokenize_function(x, tokenizer, args.max_length),
        batched=True,
        remove_columns=val_dataset.column_names,
    )

    # Load model
    print(f"Loading model: {args.model_name}")
    model = AutoModelForCausalLM.from_pretrained(
        args.model_name,
        torch_dtype=torch.bfloat16,
        device_map="auto",
        attn_implementation="eager",  # Avoid flash attention
    )

    # Setup LoRA
    logger.info("Setting up LoRA configuration...")
    lora_config = LoraConfig(
        r=args.lora_r,
        lora_alpha=args.lora_alpha,
        target_modules=[
            "q_proj",
            "k_proj",
            "v_proj",
            "o_proj",
            "gate_proj",
            "up_proj",
            "down_proj",
        ],
        lora_dropout=args.lora_dropout,
        bias="none",
        task_type=TaskType.CAUSAL_LM,
    )

    model = get_peft_model(model, lora_config)
    model.print_trainable_parameters()

    # Training arguments
    logger.info("Setting up training arguments...")
    training_args = TrainingArguments(
        output_dir=args.output_dir,
        num_train_epochs=args.epochs,
        per_device_train_batch_size=args.batch_size,
        per_device_eval_batch_size=args.batch_size,
        gradient_accumulation_steps=args.gradient_accumulation_steps,
        learning_rate=args.learning_rate,
        weight_decay=0.01,
        warmup_ratio=0.1,
        lr_scheduler_type="cosine",
        logging_steps=10,
        eval_steps=100,
        save_steps=500,
        save_total_limit=3,
        eval_strategy="steps",
        save_strategy="steps",
        load_best_model_at_end=True,
        metric_for_best_model="eval_loss",
        greater_is_better=False,
        report_to="wandb" if not args.no_wandb else "none",
        run_name=args.run_name if not args.no_wandb else None,
        dataloader_num_workers=0,  # Avoid multiprocessing issues
        fp16=False,
        bf16=True,
        gradient_checkpointing=True,
        optim="adamw_torch",
        remove_unused_columns=False,
        ddp_find_unused_parameters=False,  # Compatibility fix
    )

    # Data collator
    data_collator = DataCollatorForSeq2Seq(
        tokenizer=tokenizer,
        model=None,
        label_pad_token_id=-100,
        pad_to_multiple_of=8,
        return_tensors="pt",
        padding=True,
    )

    # Create trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=val_dataset,
        data_collator=data_collator,
        tokenizer=tokenizer,
    )

    # Train
    logger.info("Starting training...")
    try:
        trainer.train()

        # Save model
        logger.info(f"Saving model to {args.output_dir}")
        trainer.save_model()
        tokenizer.save_pretrained(args.output_dir)

        # Log final metrics
        if hasattr(trainer.state, "log_history") and trainer.state.log_history:
            final_metrics = trainer.state.log_history[-1]
            logger.info(f"Final training metrics: {final_metrics}")

        logger.info("Training completed successfully!")

    except Exception as e:
        logger.error(f"Training failed: {e}")
        raise
    finally:
        # Clean up WandB
        if not args.no_wandb:
            wandb.finish()
            logger.info("WandB session finished")


if __name__ == "__main__":
    main()
