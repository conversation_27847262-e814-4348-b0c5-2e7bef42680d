"""
Simple script to train both baseline and code-switched models
"""

import subprocess
import logging
import sys
import os

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_command(command, description=""):
    """Run a shell command and handle errors"""
    logger.info(f"Running: {description or command}")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            text=True
        )
        
        logger.info(f"✅ {description} completed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ {description} failed with return code {e.returncode}")
        return False

def main():
    """Main training function"""
    
    # Check if synchronized files exist
    baseline_file = "./data/limo_original_1000_synced.jsonl"
    code_switched_file = "./data/limo_code_switched_1000_synced.jsonl"
    
    if not os.path.exists(baseline_file):
        logger.error(f"Baseline file not found: {baseline_file}")
        logger.error("Please run the synchronization script first:")
        logger.error("python sync_datasets_advanced.py")
        sys.exit(1)
    
    if not os.path.exists(code_switched_file):
        logger.error(f"Code-switched file not found: {code_switched_file}")
        logger.error("Please run the synchronization script first:")
        logger.error("python sync_datasets_advanced.py")
        sys.exit(1)
    
    logger.info("=" * 60)
    logger.info("TRAINING BOTH MODELS")
    logger.info("=" * 60)
    
    # Train baseline model
    logger.info("1. Training baseline model...")
    baseline_cmd = (
        "python finetune.py "
        f"--train_file {baseline_file} "
        "--output_dir ./models/baseline_synced "
        "--epochs 3 "
        "--batch_size 2 "
        "--learning_rate 2e-4"
    )
    
    success = run_command(baseline_cmd, "Baseline model training")
    if not success:
        logger.error("Baseline training failed. Stopping.")
        sys.exit(1)
    
    # Train code-switched model
    logger.info("2. Training code-switched model...")
    code_switched_cmd = (
        "python finetune.py "
        f"--train_file {code_switched_file} "
        "--output_dir ./models/code_switched_synced "
        "--epochs 3 "
        "--batch_size 2 "
        "--learning_rate 2e-4"
    )
    
    success = run_command(code_switched_cmd, "Code-switched model training")
    if not success:
        logger.error("Code-switched training failed.")
        sys.exit(1)
    
    # Summary
    logger.info("=" * 60)
    logger.info("TRAINING COMPLETE!")
    logger.info("=" * 60)
    logger.info("Both models have been trained successfully:")
    logger.info("  Baseline model: ./models/baseline_synced")
    logger.info("  Code-switched model: ./models/code_switched_synced")
    logger.info("")
    logger.info("You can now evaluate and compare the models!")

if __name__ == "__main__":
    main()
