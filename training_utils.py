"""
Training utilities for fine-tuning Qwen2.5-7B-Instruct
"""

import torch
import logging
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    BitsAndBytesConfig,
    TrainingArguments,
    DataCollatorForSeq2Seq,
)
from peft import LoraConfig, get_peft_model, TaskType
from datasets import Dataset
from typing import Dict, Any, Optional

from config import Config

logger = logging.getLogger(__name__)


class ModelSetup:
    """Handles model and tokenizer setup"""

    def __init__(self, config: Config):
        self.config = config
        self.model = None
        self.tokenizer = None

    def setup_tokenizer(self) -> AutoTokenizer:
        """Setup tokenizer with proper configuration"""
        logger.info(f"Loading tokenizer: {self.config.model.model_name}")

        tokenizer = AutoTokenizer.from_pretrained(
            self.config.model.model_name,
            cache_dir=self.config.cache_dir,
            trust_remote_code=True,
        )

        # Add special tokens if needed
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token

        # Set padding side for training
        tokenizer.padding_side = "right"

        self.tokenizer = tokenizer
        logger.info("Tokenizer loaded successfully")
        return tokenizer

    def setup_quantization_config(self) -> Optional[BitsAndBytesConfig]:
        """Setup quantization configuration"""
        if not (self.config.model.load_in_4bit or self.config.model.load_in_8bit):
            return None

        logger.info("Setting up quantization configuration")

        if self.config.model.load_in_4bit:
            quantization_config = BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_compute_dtype=torch.float16,
                bnb_4bit_quant_type="nf4",
                bnb_4bit_use_double_quant=True,
            )
            logger.info("4-bit quantization enabled")
        else:
            quantization_config = BitsAndBytesConfig(load_in_8bit=True)
            logger.info("8-bit quantization enabled")

        return quantization_config

    def setup_model(self) -> AutoModelForCausalLM:
        """Setup model with proper configuration"""
        logger.info(f"Loading model: {self.config.model.model_name}")

        quantization_config = self.setup_quantization_config()

        model_kwargs = {
            "cache_dir": self.config.cache_dir,
            "trust_remote_code": True,
            "torch_dtype": (
                "auto"
                if self.config.model.torch_dtype == "auto"
                else getattr(torch, self.config.model.torch_dtype)
            ),
            "device_map": "auto",
        }

        if quantization_config:
            model_kwargs["quantization_config"] = quantization_config

        if self.config.model.use_flash_attention:
            model_kwargs["attn_implementation"] = "flash_attention_2"

        model = AutoModelForCausalLM.from_pretrained(
            self.config.model.model_name, **model_kwargs
        )

        # Enable gradient checkpointing for memory efficiency
        if self.config.training.gradient_checkpointing:
            model.gradient_checkpointing_enable()

        self.model = model
        logger.info("Model loaded successfully")
        return model

    def setup_lora(self, model: AutoModelForCausalLM) -> AutoModelForCausalLM:
        """Setup LoRA for parameter-efficient fine-tuning"""
        logger.info("Setting up LoRA configuration")

        lora_config = LoraConfig(
            r=self.config.lora.r,
            lora_alpha=self.config.lora.lora_alpha,
            target_modules=self.config.lora.target_modules,
            lora_dropout=self.config.lora.lora_dropout,
            bias=self.config.lora.bias,
            task_type=TaskType.CAUSAL_LM,
        )

        model = get_peft_model(model, lora_config)

        # Print trainable parameters
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        total_params = sum(p.numel() for p in model.parameters())

        logger.info(f"Trainable parameters: {trainable_params:,}")
        logger.info(f"Total parameters: {total_params:,}")
        logger.info(
            f"Trainable percentage: {100 * trainable_params / total_params:.2f}%"
        )

        return model


class DatasetProcessor:
    """Handles dataset processing for training"""

    def __init__(self, config: Config, tokenizer: AutoTokenizer):
        self.config = config
        self.tokenizer = tokenizer

    def tokenize_function(self, examples):
        """Tokenize the dataset examples"""
        # Apply chat template to format the conversation
        texts = []
        for messages in examples["messages"]:
            text = self.tokenizer.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=False
            )
            texts.append(text)

        # Tokenize the formatted text
        tokenized = self.tokenizer(
            texts,
            truncation=True,
            padding=False,
            max_length=self.config.model.max_length,
            return_tensors=None,
        )

        # For causal language modeling, labels are the same as input_ids
        tokenized["labels"] = tokenized["input_ids"].copy()

        return tokenized

    def process_datasets(self, train_dataset: Dataset, val_dataset: Dataset):
        """Process datasets for training"""
        logger.info("Processing datasets for training")

        # Tokenize datasets
        train_dataset = train_dataset.map(
            self.tokenize_function,
            batched=True,
            remove_columns=train_dataset.column_names,
            desc="Tokenizing train dataset",
        )

        val_dataset = val_dataset.map(
            self.tokenize_function,
            batched=True,
            remove_columns=val_dataset.column_names,
            desc="Tokenizing validation dataset",
        )

        logger.info(f"Processed train dataset: {len(train_dataset)} samples")
        logger.info(f"Processed val dataset: {len(val_dataset)} samples")

        return train_dataset, val_dataset


def setup_training_arguments(config: Config) -> TrainingArguments:
    """Setup training arguments"""
    logger.info("Setting up training arguments")

    training_args = TrainingArguments(
        output_dir=config.training.output_dir,
        num_train_epochs=config.training.num_train_epochs,
        per_device_train_batch_size=config.training.per_device_train_batch_size,
        per_device_eval_batch_size=config.training.per_device_eval_batch_size,
        gradient_accumulation_steps=config.training.gradient_accumulation_steps,
        learning_rate=config.training.learning_rate,
        weight_decay=config.training.weight_decay,
        warmup_ratio=config.training.warmup_ratio,
        lr_scheduler_type=config.training.lr_scheduler_type,
        logging_steps=config.training.logging_steps,
        eval_steps=config.training.eval_steps,
        save_steps=config.training.save_steps,
        save_total_limit=config.training.save_total_limit,
        eval_strategy=config.training.evaluation_strategy,
        save_strategy=config.training.save_strategy,
        load_best_model_at_end=config.training.load_best_model_at_end,
        metric_for_best_model=config.training.metric_for_best_model,
        greater_is_better=config.training.greater_is_better,
        report_to=config.training.report_to,
        run_name=config.training.run_name,
        dataloader_num_workers=config.training.dataloader_num_workers,
        fp16=config.training.fp16,
        bf16=config.training.bf16,
        gradient_checkpointing=config.training.gradient_checkpointing,
        optim=config.training.optim,
        group_by_length=config.training.group_by_length,
        remove_unused_columns=config.training.remove_unused_columns,
        push_to_hub=False,
        hub_model_id=None,
    )

    return training_args


def setup_data_collator(tokenizer: AutoTokenizer) -> DataCollatorForSeq2Seq:
    """Setup data collator for sequence-to-sequence tasks"""
    return DataCollatorForSeq2Seq(
        tokenizer=tokenizer,
        model=None,  # Will be set automatically
        label_pad_token_id=-100,
        pad_to_multiple_of=8,
        return_tensors="pt",
        padding=True,
    )
